{"packages": [{"name": "abraham/twitteroauth", "version": "4.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/abraham/twitteroauth.git", "reference": "b9302599e416e5c00742cf7f4455220897f8291d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/abraham/twitteroauth/zipball/b9302599e416e5c00742cf7f4455220897f8291d", "reference": "b9302599e416e5c00742cf7f4455220897f8291d", "shasum": ""}, "require": {"composer/ca-bundle": "^1.2", "ext-curl": "*", "php": "^7.4 || ^8.0 || ^8.1"}, "require-dev": {"php-vcr/php-vcr": "^1", "php-vcr/phpunit-testlistener-vcr": "dev-php-8", "phpmd/phpmd": "^2", "phpunit/phpunit": "^8 || ^9", "rector/rector": "^0.12.19 || ^0.13.0", "squizlabs/php_codesniffer": "^3"}, "time": "2022-08-18T23:30:33+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Abraham\\TwitterOAuth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://abrah.am", "role": "Developer"}], "description": "The most popular PHP library for use with the Twitter OAuth REST API.", "homepage": "https://twitteroauth.com", "keywords": ["Twitter API", "Twitter oAuth", "api", "o<PERSON>h", "rest", "social", "twitter"], "support": {"issues": "https://github.com/abraham/twitteroauth/issues", "source": "https://github.com/abraham/twitteroauth"}, "install-path": "../abraham/twitteroauth"}, {"name": "composer/ca-bundle", "version": "dev-main", "version_normalized": "dev-main", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "74780ccf8c19d6acb8d65c5f39cd72110e132bbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/74780ccf8c19d6acb8d65c5f39cd72110e132bbd", "reference": "74780ccf8c19d6acb8d65c5f39cd72110e132bbd", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "time": "2023-01-11T08:27:00+00:00", "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./ca-bundle"}], "dev": false, "dev-package-names": []}