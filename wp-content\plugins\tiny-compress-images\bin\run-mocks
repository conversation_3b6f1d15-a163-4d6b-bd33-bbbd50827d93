#!/bin/bash

port="$1"

docker compose -f config/mocks.docker-compose.yml up -d

mv src/vendor/tinify/Tinify/Client.php src/vendor/tinify/Tinify/Client.php.bak
cp test/fixtures/Client.php src/vendor/tinify/Tinify/Client.php

mv src/config/class-tiny-config.php src/config/class-tiny-config.php.bak
cp test/fixtures/class-tiny-config.php src/config/class-tiny-config.php

echo "Replacing configuration files..."
mv src/vendor/tinify/Tinify/Client.php src/vendor/tinify/Tinify/Client.php.bak
cp test/fixtures/Client.php src/vendor/tinify/Tinify/Client.php

mv src/config/class-tiny-config.php src/config/class-tiny-config.php.bak
cp test/fixtures/class-tiny-config.php src/config/class-tiny-config.php

echo "To stop, run: bin/stop-mocks"