<?xml version="1.0"?>
<ruleset>
  <config name="installed_paths" value="vendor/wp-coding-standards/wpcs" />

  <rule ref="WordPress-Core" />
  <rule ref="Generic.Files.LineLength">
    <properties>
      <property name="lineLimit" value="100" />
      <property name="absoluteLineLimit" value="100" />
      <property name="ignoreComments" value="true" />
    </properties>
    <exclude-pattern>src/views</exclude-pattern>
  </rule>
  <rule ref="WordPress.WP.I18n.UnorderedPlaceholders">
    <severity>0</severity>
  </rule>

  <file>src</file>
  <file>test</file>
  <exclude-pattern>src/css</exclude-pattern>
  <exclude-pattern>src/data</exclude-pattern>
  <exclude-pattern>src/js</exclude-pattern>
  <exclude-pattern>vendor</exclude-pattern>
  <exclude-pattern>test/</exclude-pattern>
</ruleset>