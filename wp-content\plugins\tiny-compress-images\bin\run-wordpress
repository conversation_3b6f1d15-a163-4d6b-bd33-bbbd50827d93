#!/bin/bash

display_usage() {
  echo -e "Usage:\n\t$0 <version> \n\nExample:\n\t$0 45"
}

if [ -z "${WORDPRESS_VERSION}" ]; then
  if [ $# -eq 0 ]; then
    display_usage
    exit 1
  fi

  wp_version_input="$1"
  php_version_input="$2"

  ## WordPress version ex. "6.7"
  export WORDPRESS_VERSION_FULL="${wp_version_input:0:1}.${wp_version_input:1}"

  ## WordPress version ex "67"
  export WORDPRESS_VERSION="${wp_version_input}"

  ## PHP Version ex "80"
  export PHP_VERSION="${php_version_input}"

  ## PHP Version ex "80"
  export PHP_VERSION_FULL="${PHP_VERSION:0:1}.${PHP_VERSION:1}"
  
  echo "Starting WordPress ${WORDPRESS_VERSION_FULL} on PHP ${PHP_VERSION_FULL}"
fi

export WP_ENV_TESTS_PORT=80${WORDPRESS_VERSION}
export WP_ENV_CORE=WordPress/Wordpress#${WORDPRESS_VERSION_FULL}
export WP_ENV_PHP_VERSION=${PHP_VERSION_FULL}

npx wp-env start || exit 1

echo "Installing compatible plugins.."

if [ "$WORDPRESS_VERSION" -ge 55 ]; then
  echo "Installing compatible plugins for WordPress ${WORDPRESS_VERSION_FULL}.."
  npx wp-env run tests-cli wp plugin install amazon-s3-and-cloudfront || exit 1
fi

echo "To stop, run: bin/stop-wordpress"
