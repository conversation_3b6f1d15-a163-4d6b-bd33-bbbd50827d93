name: Check and Test

on:
  push:
    branches:
      - '**'
  pull_request:
    branches:
      - '**'

jobs:
  check:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        php: ['7.4']
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup
        with:
          php: ${{ matrix.php }}

      - name: PHPCS
        run: ./bin/check-style

  test:
    needs: check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        php: ['7.4']
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup
        uses: ./.github/actions/setup
        with:
          php: ${{ matrix.php }}

      - name: PHPUnit
        run: ./bin/unit-tests
