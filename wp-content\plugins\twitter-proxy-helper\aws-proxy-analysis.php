<?php
/**
 * AWS韩国服务器中转配置分析工具
 * 分析和优化中转服务器设置
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// 检查权限
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>AWS韩国服务器中转配置分析</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .analysis-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .analysis-result.success { border-left-color: green; background: #f0fff0; }
        .analysis-result.error { border-left-color: red; background: #fff0f0; }
        .analysis-result.warning { border-left-color: orange; background: #fff8f0; }
        .analysis-result.info { border-left-color: blue; background: #f0f8ff; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; }
        .recommendation { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .button { padding: 10px 20px; margin: 5px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; border: none; cursor: pointer; }
        .button:hover { background: #005a87; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🌐 AWS韩国服务器中转配置分析</h1>
    <p>分析当前代理配置并提供优化建议</p>
    
    <div class="section">
        <h2>1. 当前代理配置分析</h2>
        <?php
        $proxy_config = [
            'server' => '************',
            'port' => 7890,
            'type' => 'SOCKS5',
            'region' => '韩国首尔 (ap-northeast-2)',
            'provider' => 'AWS EC2'
        ];
        
        echo '<table>';
        echo '<tr><th>配置项</th><th>当前值</th><th>状态</th></tr>';
        foreach ($proxy_config as $key => $value) {
            $status = '<span class="success">✅ 正常</span>';
            if ($key === 'port' && $value == 7890) {
                $status = '<span class="info">ℹ️ 常用SOCKS5端口</span>';
            }
            echo '<tr><td>' . ucfirst($key) . '</td><td>' . $value . '</td><td>' . $status . '</td></tr>';
        }
        echo '</table>';
        ?>
    </div>
    
    <div class="section">
        <h2>2. 网络连通性测试</h2>
        <?php
        // 测试到代理服务器的连接
        echo '<h3>2.1 代理服务器连通性</h3>';
        
        $proxy_host = '************';
        $proxy_port = 7890;
        
        // 使用fsockopen测试端口连通性
        $start_time = microtime(true);
        $connection = @fsockopen($proxy_host, $proxy_port, $errno, $errstr, 10);
        $connect_time = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($connection) {
            fclose($connection);
            echo '<div class="analysis-result success">✅ 代理服务器端口 ' . $proxy_port . ' 可达 (延迟: ' . $connect_time . 'ms)</div>';
        } else {
            echo '<div class="analysis-result error">❌ 无法连接到代理服务器: ' . $errstr . ' (错误码: ' . $errno . ')</div>';
        }
        
        // 测试通过代理访问Twitter
        echo '<h3>2.2 通过代理访问Twitter API</h3>';
        
        $test_urls = [
            'Twitter API v2' => 'https://api.twitter.com/2/openapi.json',
            'Twitter Upload' => 'https://upload.twitter.com/',
            'Twitter X API' => 'https://api.x.com/'
        ];
        
        foreach ($test_urls as $name => $url) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_PROXY, $proxy_host);
            curl_setopt($ch, CURLOPT_PROXYPORT, $proxy_port);
            curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部
            
            $start_time = microtime(true);
            $result = curl_exec($ch);
            $request_time = round((microtime(true) - $start_time) * 1000, 2);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo '<div class="analysis-result error">❌ ' . $name . ': ' . $error . '</div>';
            } else {
                $status_class = ($http_code >= 200 && $http_code < 400) ? 'success' : 'warning';
                echo '<div class="analysis-result ' . $status_class . '">✅ ' . $name . ': HTTP ' . $http_code . ' (响应时间: ' . $request_time . 'ms)</div>';
            }
        }
        ?>
    </div>
    
    <div class="section">
        <h2>3. 中转服务器优化建议</h2>
        
        <div class="recommendation">
            <h3>🔧 服务器端配置优化建议</h3>
            
            <h4>3.1 SOCKS5代理服务配置</h4>
            <p>建议在AWS韩国服务器上使用以下配置：</p>
            
            <div class="code-block">
# 推荐使用 3proxy 或 dante-server
# 3proxy 配置示例 (/etc/3proxy/3proxy.cfg)

# 基本配置
daemon
pidfile /var/run/3proxy.pid
nserver *******
nserver *******
nscache 65536
timeouts 1 5 30 60 180 1800 15 60

# 日志配置
log /var/log/3proxy.log D
logformat "- +_L%t.%. %N.%p %E %U %C:%c %R:%r %O %I %h %T"
rotate 30

# 访问控制
auth none
allow * * * 80-88,8080-8088 HTTP
allow * * * 443,8443 HTTPS  
allow * * * 1-65535 CONNECT

# SOCKS5代理监听
socks -p7890
            </div>
            
            <h4>3.2 系统级优化</h4>
            <div class="code-block">
# /etc/sysctl.conf 网络优化
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq

# 应用配置
sudo sysctl -p
            </div>
            
            <h4>3.3 防火墙配置</h4>
            <div class="code-block">
# AWS安全组规则
# 入站规则：
# - 类型: 自定义TCP, 端口: 7890, 源: 您的服务器IP
# - 类型: SSH, 端口: 22, 源: 您的管理IP

# 服务器防火墙 (ufw)
sudo ufw allow from YOUR_SERVER_IP to any port 7890
sudo ufw allow ssh
sudo ufw enable
            </div>
        </div>
        
        <div class="recommendation">
            <h3>⚡ 性能优化建议</h3>
            
            <h4>3.4 连接池和复用</h4>
            <p>建议在WordPress端实现连接复用：</p>
            
            <div class="code-block">
// 在 twitter-proxy-helper.php 中添加连接复用
function twitter_proxy_get_persistent_connection() {
    static $connection_pool = [];
    
    $key = '************:7890';
    
    if (!isset($connection_pool[$key])) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_PROXY, '************');
        curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TCP_KEEPALIVE, 1);
        curl_setopt($ch, CURLOPT_TCP_KEEPIDLE, 120);
        curl_setopt($ch, CURLOPT_TCP_KEEPINTVL, 60);
        
        $connection_pool[$key] = $ch;
    }
    
    return $connection_pool[$key];
}
            </div>
            
            <h4>3.5 智能重试和故障转移</h4>
            <p>建议配置备用代理服务器：</p>
            
            <div class="code-block">
// 多代理服务器配置
$proxy_servers = [
    'primary' => '************:7890',    // 韩国首尔
    'backup1' => '13.125.xxx.xxx:7890',  // 韩国首尔备用
    'backup2' => '18.179.xxx.xxx:7890',  // 日本东京
];

function twitter_proxy_get_available_proxy() {
    global $proxy_servers;
    
    foreach ($proxy_servers as $name => $proxy) {
        if (twitter_proxy_test_connection($proxy)) {
            return $proxy;
        }
    }
    
    return false; // 所有代理都不可用
}
            </div>
        </div>
    </div>
    
    <div class="section">
        <h2>4. 监控和维护建议</h2>
        
        <div class="analysis-result info">
            <h3>📊 监控指标</h3>
            <ul>
                <li><strong>连接成功率</strong>: 应保持在95%以上</li>
                <li><strong>平均响应时间</strong>: 应控制在2秒以内</li>
                <li><strong>代理服务器负载</strong>: CPU和内存使用率</li>
                <li><strong>网络带宽</strong>: 监控流量使用情况</li>
            </ul>
        </div>
        
        <div class="analysis-result warning">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li><strong>IP白名单</strong>: 确保只有您的服务器可以访问代理</li>
                <li><strong>流量限制</strong>: 监控AWS数据传输费用</li>
                <li><strong>合规性</strong>: 确保符合Twitter API使用条款</li>
                <li><strong>备份方案</strong>: 准备多个地区的备用服务器</li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h2>5. 自动化部署脚本</h2>
        <p>以下是在AWS韩国服务器上快速部署SOCKS5代理的脚本：</p>
        
        <div class="code-block">
#!/bin/bash
# AWS韩国服务器SOCKS5代理部署脚本

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装3proxy
sudo apt install -y 3proxy

# 创建配置文件
sudo tee /etc/3proxy/3proxy.cfg > /dev/null << 'EOF'
daemon
pidfile /var/run/3proxy.pid
nserver *******
nserver *******
nscache 65536
timeouts 1 5 30 60 180 1800 15 60

log /var/log/3proxy.log D
logformat "- +_L%t.%. %N.%p %E %U %C:%c %R:%r %O %I %h %T"
rotate 30

auth none
allow * * * 80-88,8080-8088 HTTP
allow * * * 443,8443 HTTPS  
allow * * * 1-65535 CONNECT

socks -p7890
EOF

# 启动服务
sudo systemctl enable 3proxy
sudo systemctl start 3proxy

# 配置防火墙
sudo ufw allow 7890/tcp
sudo ufw --force enable

echo "SOCKS5代理服务已部署完成！"
echo "代理地址: $(curl -s ifconfig.me):7890"
        </div>
    </div>
    
    <div class="section">
        <h2>6. 测试和验证</h2>
        <form method="post">
            <button type="submit" name="test_action" value="full_test" class="button">
                🧪 执行完整测试
            </button>
            <button type="submit" name="test_action" value="speed_test" class="button">
                ⚡ 速度测试
            </button>
            <button type="submit" name="test_action" value="stability_test" class="button">
                🔄 稳定性测试
            </button>
        </form>
        
        <?php
        if (isset($_POST['test_action'])) {
            $action = $_POST['test_action'];
            
            echo '<h3>测试结果</h3>';
            
            switch ($action) {
                case 'full_test':
                    echo '<div class="analysis-result info">正在执行完整测试...</div>';
                    // 这里可以添加完整的测试逻辑
                    break;
                    
                case 'speed_test':
                    echo '<div class="analysis-result info">正在测试连接速度...</div>';
                    // 速度测试逻辑
                    break;
                    
                case 'stability_test':
                    echo '<div class="analysis-result info">正在测试连接稳定性...</div>';
                    // 稳定性测试逻辑
                    break;
            }
        }
        ?>
    </div>
    
    <p><strong>分析完成时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
</body>
</html>
