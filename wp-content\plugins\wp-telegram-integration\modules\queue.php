<?php
/**
 * 队列处理模块
 * 
 * 提供消息队列管理和处理功能
 */

// 禁止直接访问
if (!defined('WPINC')) {
    die;
}

/**
 * 添加文章到队列
 * 
 * @param int $post_id 文章ID
 * @return bool 是否成功添加
 */
function wp_telegram_add_to_queue($post_id) {
    $queue = get_option('wp_telegram_integration_queue', array());
    
    // 检查是否已在队列中
    if (in_array($post_id, $queue)) {
        wp_telegram_log('文章ID ' . $post_id . ' 已在队列中', 'debug');
        return false;
    }
    
    // 添加到队列
    $queue[] = $post_id;
    update_option('wp_telegram_integration_queue', $queue);
    
    // 设置重试次数初始值
    update_post_meta($post_id, '_telegram_integration_retry_count', 0);
    
    // 确保定时任务已设置
    if (!wp_next_scheduled('wp_telegram_integration_queue_cron')) {
        wp_schedule_event(time(), 'minute', 'wp_telegram_integration_queue_cron');
        wp_telegram_log('定时任务已设置，一分钟后开始处理队列', 'info');
    }
    
    wp_telegram_log('文章ID ' . $post_id . ' 已添加到队列', 'info');
    return true;
}

/**
 * 从队列移除文章
 * 
 * @param int $post_id 文章ID
 * @return bool 是否成功移除
 */
function wp_telegram_remove_from_queue($post_id) {
    $queue = get_option('wp_telegram_integration_queue', array());
    
    // 检查是否在队列中
    $key = array_search($post_id, $queue);
    if ($key === false) {
        wp_telegram_log('文章ID ' . $post_id . ' 不在队列中', 'debug');
        return false;
    }
    
    // 从队列移除
    unset($queue[$key]);
    $queue = array_values($queue); // 重新索引数组
    update_option('wp_telegram_integration_queue', $queue);
    
    wp_telegram_log('文章ID ' . $post_id . ' 已从队列移除', 'debug');
    
    // 如果队列为空，取消定时任务
    if (empty($queue)) {
        $timestamp = wp_next_scheduled('wp_telegram_integration_queue_cron');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'wp_telegram_integration_queue_cron');
            wp_telegram_log('队列为空，定时任务已取消', 'debug');
        }
    }
    
    return true;
}

/**
 * 自动处理队列的定时任务
 */
function wp_telegram_process_queue_automatically() {
    wp_telegram_log('开始自动处理队列', 'debug');
    
    $queue = get_option('wp_telegram_integration_queue', array());
    if (empty($queue)) {
        wp_telegram_log('队列为空，无需处理', 'debug');
        
        // 确保定时任务被清理
        $timestamp = wp_next_scheduled('wp_telegram_integration_queue_cron');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'wp_telegram_integration_queue_cron');
            wp_telegram_log('定时任务已清理', 'debug');
        }
        
        return;
    }
    
    // 取出队列中的第一项
    $post_id = array_shift($queue);
    $retry_count = (int) get_post_meta($post_id, '_telegram_integration_retry_count', true);
    
    wp_telegram_log('处理队列中的文章ID: ' . $post_id . '，重试次数: ' . $retry_count, 'debug');
    
    // 检查重试次数是否超过限制
    $max_retries = 5; // 最大重试次数
    if ($retry_count >= $max_retries) {
        wp_telegram_log('文章ID ' . $post_id . ' 超过最大重试次数，放弃推送', 'warning');
        update_post_meta($post_id, '_telegram_integration_failed', true);
        update_option('wp_telegram_integration_queue', $queue);
        return;
    }
    
    // 获取文章
    $post = get_post($post_id);
    if (!$post || $post->post_status != 'publish') {
        wp_telegram_log('文章ID ' . $post_id . ' 不存在或未发布，从队列移除', 'warning');
        update_option('wp_telegram_integration_queue', $queue);
        return;
    }
    
    // 尝试发送
    $result = wp_telegram_publish_post($post_id, $post, false);
    
    if ($result === true) {
        wp_telegram_log('队列推送成功: 文章ID ' . $post_id, 'info');
    } else {
        // 推送失败，增加重试次数并放回队列尾部
        $retry_count++;
        update_post_meta($post_id, '_telegram_integration_retry_count', $retry_count);
        array_push($queue, $post_id);
        wp_telegram_log('队列推送失败: 文章ID ' . $post_id . '，重试次数增加到 ' . $retry_count, 'warning');
    }
    
    // 更新队列
    update_option('wp_telegram_integration_queue', $queue);
    
    // 如果队列为空，取消定时任务
    if (empty($queue)) {
        $timestamp = wp_next_scheduled('wp_telegram_integration_queue_cron');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'wp_telegram_integration_queue_cron');
            wp_telegram_log('队列处理完毕，定时任务已取消', 'info');
        }
    }
}
add_action('wp_telegram_integration_queue_cron', 'wp_telegram_process_queue_automatically');

/**
 * 手动处理整个队列
 * 
 * @param int $limit 处理的最大项数，默认为20
 * @return int 成功处理的项数
 */
function wp_telegram_process_queue_manually($limit = 20) {
    wp_telegram_log('开始手动处理队列，最大处理 ' . $limit . ' 项', 'info');
    
    $queue = get_option('wp_telegram_integration_queue', array());
    if (empty($queue)) {
        wp_telegram_log('队列为空，无需处理', 'info');
        return 0;
    }
    
    $processed = 0;
    $success = 0;
    $new_queue = $queue;
    
    // 处理队列中的项目，直到达到限制或队列为空
    while ($processed < $limit && !empty($new_queue)) {
        $post_id = array_shift($new_queue);
        $processed++;
        
        wp_telegram_log('手动处理队列项 ' . $processed . '/' . $limit . '，文章ID: ' . $post_id, 'debug');
        
        $post = get_post($post_id);
        if (!$post || $post->post_status != 'publish') {
            wp_telegram_log('文章ID ' . $post_id . ' 不存在或未发布，跳过', 'warning');
            continue;
        }
        
        // 尝试发送
        $result = wp_telegram_publish_post($post_id, $post, false);
        
        if ($result === true) {
            $success++;
            wp_telegram_log('手动推送成功: 文章ID ' . $post_id, 'info');
        } else {
            // 如果失败，放回队列尾部
            $retry_count = (int) get_post_meta($post_id, '_telegram_integration_retry_count', true) + 1;
            update_post_meta($post_id, '_telegram_integration_retry_count', $retry_count);
            
            if ($retry_count < 5) {
                $new_queue[] = $post_id;
                wp_telegram_log('手动推送失败: 文章ID ' . $post_id . '，重试次数增加到 ' . $retry_count, 'warning');
            } else {
                wp_telegram_log('文章ID ' . $post_id . ' 超过最大重试次数，放弃推送', 'warning');
                update_post_meta($post_id, '_telegram_integration_failed', true);
            }
        }
    }
    
    // 更新队列
    update_option('wp_telegram_integration_queue', $new_queue);
    
    // 如果队列为空，取消定时任务
    if (empty($new_queue)) {
        $timestamp = wp_next_scheduled('wp_telegram_integration_queue_cron');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'wp_telegram_integration_queue_cron');
            wp_telegram_log('队列处理完毕，定时任务已取消', 'info');
        }
    }
    
    wp_telegram_log('手动队列处理完成，处理了 ' . $processed . ' 项，成功 ' . $success . ' 项', 'info');
    return $processed;
}

/**
 * 获取队列状态信息
 * 
 * @return array 队列状态信息
 */
function wp_telegram_get_queue_status() {
    $queue = get_option('wp_telegram_integration_queue', array());
    $queue_count = count($queue);
    
    // 获取失败文章数量
    $failed_args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_telegram_integration_failed',
                'value' => '1',
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'fields' => 'ids'
    );
    $failed_query = new WP_Query($failed_args);
    $failed_count = $failed_query->found_posts;
    
    // 获取成功文章数量
    $success_args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_telegram_integration_sent',
                'value' => '1',
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'fields' => 'ids'
    );
    $success_query = new WP_Query($success_args);
    $success_count = $success_query->found_posts;
    
    // 获取下次定时任务执行时间
    $next_cron = wp_next_scheduled('wp_telegram_integration_queue_cron');
    
    return array(
        'queue_count' => $queue_count,
        'failed_count' => $failed_count,
        'success_count' => $success_count,
        'next_cron' => $next_cron ? date('Y-m-d H:i:s', $next_cron) : null
    );
}

/**
 * 重置失败状态，允许重新推送
 * 
 * @param int $post_id 文章ID
 * @return bool 是否成功重置
 */
function wp_telegram_reset_failed_status($post_id) {
    delete_post_meta($post_id, '_telegram_integration_failed');
    delete_post_meta($post_id, '_telegram_integration_retry_count');
    delete_post_meta($post_id, '_telegram_integration_sent');
    
    wp_telegram_log('重置文章ID ' . $post_id . ' 的失败状态', 'info');
    return true;
}

/**
 * 添加队列管理页面
 */
function wp_telegram_queue_menu() {
    add_submenu_page(
        'options-general.php',
        'Telegram推送队列',
        'Telegram队列',
        'manage_options',
        'wp-telegram-queue',
        'wp_telegram_queue_page'
    );
}
add_action('admin_menu', 'wp_telegram_queue_menu');

/**
 * 队列管理页面
 */
function wp_telegram_queue_page() {
    // 确认用户权限
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 处理清空队列请求
    if (isset($_POST['wp_telegram_clear_queue']) && check_admin_referer('wp_telegram_clear_queue', 'wp_telegram_clear_queue_nonce')) {
        update_option('wp_telegram_integration_queue', array());
        echo '<div class="notice notice-success is-dismissible"><p>队列已清空。</p></div>';
    }
    
    // 处理重置失败状态请求
    if (isset($_POST['wp_telegram_reset_failed']) && check_admin_referer('wp_telegram_reset_failed', 'wp_telegram_reset_failed_nonce')) {
        // 查找所有标记为失败的文章
        $failed_args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_telegram_integration_failed',
                    'value' => '1',
                    'compare' => '='
                )
            ),
            'posts_per_page' => -1,
            'fields' => 'ids'
        );
        $failed_query = new WP_Query($failed_args);
        
        // 重置所有失败文章的状态
        $count = 0;
        foreach ($failed_query->posts as $post_id) {
            wp_telegram_reset_failed_status($post_id);
            wp_telegram_add_to_queue($post_id);
            $count++;
        }
        
        echo '<div class="notice notice-success is-dismissible"><p>已重置 ' . $count . ' 篇失败文章的状态并重新加入队列。</p></div>';
    }
    
    // 处理手动推送请求
    if (isset($_POST['wp_telegram_manual_push']) && check_admin_referer('wp_telegram_manual_push', 'wp_telegram_manual_push_nonce') && isset($_POST['post_id'])) {
        $post_id = intval($_POST['post_id']);
        $post = get_post($post_id);
        
        if ($post && $post->post_status == 'publish') {
            // 重置状态并尝试推送
            wp_telegram_reset_failed_status($post_id);
            $result = wp_telegram_publish_post($post_id, $post, false);
            
            if ($result === true) {
                echo '<div class="notice notice-success is-dismissible"><p>文章 "' . esc_html($post->post_title) . '" 已成功推送到Telegram。</p></div>';
            } else {
                echo '<div class="notice notice-error is-dismissible"><p>推送失败: ' . ($result instanceof WP_Error ? $result->get_error_message() : '未知错误') . '</p></div>';
            }
        } else {
            echo '<div class="notice notice-error is-dismissible"><p>文章不存在或未发布。</p></div>';
        }
    }
    
    // 获取队列状态
    $queue_status = wp_telegram_get_queue_status();
    
    // 获取队列中的文章
    $queue = get_option('wp_telegram_integration_queue', array());
    $queue_posts = array();
    
    foreach ($queue as $post_id) {
        $post = get_post($post_id);
        if ($post) {
            $retry_count = (int) get_post_meta($post_id, '_telegram_integration_retry_count', true);
            $queue_posts[] = array(
                'id' => $post_id,
                'title' => $post->post_title,
                'retry_count' => $retry_count
            );
        }
    }
    
    // 获取失败的文章
    $failed_args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_telegram_integration_failed',
                'value' => '1',
                'compare' => '='
            )
        ),
        'posts_per_page' => 20
    );
    $failed_query = new WP_Query($failed_args);
    
    // 页面输出
    ?>
    <div class="wrap">
        <h1>Telegram推送队列管理</h1>
        
        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>队列状态</h2>
            <p>当前队列中有 <strong><?php echo $queue_status['queue_count']; ?></strong> 个待处理项</p>
            <p>已成功推送 <strong><?php echo $queue_status['success_count']; ?></strong> 篇文章</p>
            <p>推送失败 <strong><?php echo $queue_status['failed_count']; ?></strong> 篇文章</p>
            
            <?php if ($queue_status['next_cron']): ?>
                <p>下一次自动处理计划在: <strong><?php echo $queue_status['next_cron']; ?></strong></p>
            <?php else: ?>
                <p>没有计划的自动处理任务</p>
            <?php endif; ?>
            
            <div style="display: flex; gap: 10px; margin-top: 15px;">
                <form method="post" action="">
                    <?php wp_nonce_field('wp_telegram_clear_queue', 'wp_telegram_clear_queue_nonce'); ?>
                    <input type="hidden" name="wp_telegram_clear_queue" value="1">
                    <?php submit_button('清空队列', 'delete', 'clear_queue', false, array('onclick' => 'return confirm("确定要清空队列吗？此操作不可撤销。");')); ?>
                </form>
                
                <form method="post" action="">
                    <?php wp_nonce_field('wp_telegram_reset_failed', 'wp_telegram_reset_failed_nonce'); ?>
                    <input type="hidden" name="wp_telegram_reset_failed" value="1">
                    <?php submit_button('重试所有失败项', 'secondary', 'reset_failed', false); ?>
                </form>
                
                <form method="post" action="">
                    <?php wp_nonce_field('wp_telegram_integration_process_queue', 'wp_telegram_integration_process_queue_nonce'); ?>
                    <input type="hidden" name="wp_telegram_integration_process_queue" value="1">
                    <?php submit_button('手动处理队列', 'primary', 'process_queue', false); ?>
                </form>
            </div>
        </div>
        
        <?php if (!empty($queue_posts)): ?>
            <div class="card" style="max-width: 800px; margin-top: 20px;">
                <h2>当前队列</h2>
                <table class="widefat striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>重试次数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($queue_posts as $post): ?>
                            <tr>
                                <td><?php echo $post['id']; ?></td>
                                <td><a href="<?php echo get_edit_post_link($post['id']); ?>" target="_blank"><?php echo esc_html($post['title']); ?></a></td>
                                <td><?php echo $post['retry_count']; ?></td>
                                <td>
                                    <form method="post" action="">
                                        <?php wp_nonce_field('wp_telegram_manual_push', 'wp_telegram_manual_push_nonce'); ?>
                                        <input type="hidden" name="wp_telegram_manual_push" value="1">
                                        <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                                        <button type="submit" class="button button-small">立即推送</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <?php if ($failed_query->have_posts()): ?>
            <div class="card" style="max-width: 800px; margin-top: 20px;">
                <h2>推送失败的文章</h2>
                <table class="widefat striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($failed_query->have_posts()): $failed_query->the_post(); ?>
                            <tr>
                                <td><?php echo get_the_ID(); ?></td>
                                <td><a href="<?php echo get_edit_post_link(); ?>" target="_blank"><?php the_title(); ?></a></td>
                                <td>
                                    <form method="post" action="">
                                        <?php wp_nonce_field('wp_telegram_manual_push', 'wp_telegram_manual_push_nonce'); ?>
                                        <input type="hidden" name="wp_telegram_manual_push" value="1">
                                        <input type="hidden" name="post_id" value="<?php echo get_the_ID(); ?>">
                                        <button type="submit" class="button button-small">重新推送</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endwhile; wp_reset_postdata(); ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

// 处理管理页面中的手动处理队列请求
function wp_telegram_handle_process_queue_request() {
    if (isset($_POST['wp_telegram_integration_process_queue']) && check_admin_referer('wp_telegram_integration_process_queue', 'wp_telegram_integration_process_queue_nonce')) {
        $processed = wp_telegram_process_queue_manually();
        add_action('admin_notices', function() use ($processed) {
            echo '<div class="notice notice-info is-dismissible"><p>手动处理了 ' . $processed . ' 个队列项</p></div>';
        });
    }
}

// 只在管理页面环境中添加这个处理函数
if (is_admin()) {
    add_action('admin_init', 'wp_telegram_handle_process_queue_request');
} 