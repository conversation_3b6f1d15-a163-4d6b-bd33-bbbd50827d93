language: php
dist: trusty
php:
- 5.4
- 5.5
- 5.6
- 7.0
- 7.1
- 7.2
- 7.3
matrix:
  include:
  - dist: precise
    php: 5.3
    script: bin/unit-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=57 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=56 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=55 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=54 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=53 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=52 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=51 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=50 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=49 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=48 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=47 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=46 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=45 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=44 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=43 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=42 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=41 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=40 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=39 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=38 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=37 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=36 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=35 INTEGRATION_TESTS=true
    script: bin/integration-tests
  - dist: trusty
    php: 7.3
    env: WORDPRESS_VERSION=34 INTEGRATION_TESTS=true
    script: bin/integration-tests
services:
  - docker
install:
- composer self-update --rollback
- composer install
- if [[ "$INTEGRATION_TESTS" == "true" ]]; then sudo service mysql stop; fi
script:
- bin/unit-tests
- bin/check-style
notifications:
  email: false
  slack:
    secure: uttJ78cpkV9czhoKXtAoop8UBWPVtF+fwKlKS47c4EW7ZfVx9jAII2yLb5yZjdLDnM7YAluujUBIqyESvwLY0h7azLsorSIEqNt9GlBi4B7DSGAdo46lFbqsdjrLwM1gOhiP7Fl4hFiQ2+jA9Fsj3W2yZ6rbF7endv7Gx5FMlFg=
