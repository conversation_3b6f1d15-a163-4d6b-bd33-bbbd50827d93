<?php
// 使用更可靠的方法找到WordPress加载文件
$wp_load_paths = array(
    dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php',  // 标准路径
    '../../../../wp-load.php',                                     // 相对路径
    '../../../wp-load.php',                                        // 备选路径1
    '../../wp-load.php',                                           // 备选路径2
    '../wp-load.php',                                              // 备选路径3
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    echo "错误：无法加载WordPress环境，请将此脚本移动到插件目录中。";
    exit;
}

// 检查当前设置
echo "当前设置：<br>";
echo "发布间隔：" . get_option('xyz_twap_post_interval', '未设置') . " 分钟<br>";
echo "队列启用：" . (get_option('xyz_twap_queue_enabled', 1) ? "是" : "否") . "<br>";
echo "最后发布时间：" . date('Y-m-d H:i:s', get_option('xyz_twap_last_tweet_time', 0)) . "<br>";

// 显示队列信息
$queue = get_option('xyz_twap_queue', array());
echo "队列中项目数量：" . count($queue) . "<br>";

// 显示下次计划运行的时间
$next_run = wp_next_scheduled('xyz_twap_process_queue');
echo "下次队列处理时间：" . ($next_run ? date('Y-m-d H:i:s', $next_run) : "未计划") . "<br>";

// 测试修改设置
if (isset($_GET['update']) && $_GET['update'] == 1) {
    $new_interval = isset($_GET['interval']) ? intval($_GET['interval']) : 15;
    update_option('xyz_twap_post_interval', $new_interval);
    echo "<br>已更新发布间隔为: " . $new_interval . " 分钟";
} 