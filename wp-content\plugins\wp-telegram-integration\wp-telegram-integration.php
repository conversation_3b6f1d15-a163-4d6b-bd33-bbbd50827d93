<?php
/**
 * Plugin Name: WP Telegram Integration
 * Plugin URI: https://wczk.wzam.cn
 * Description: WordPress与Telegram集成插件，整合文章同步、图片修复、付费内容支持和代理设置等功能
 * Version: 1.0.0
 * Author: Claude
 * Text Domain: wp-telegram-integration
 */

// 如果直接访问此文件，则退出
if (!defined('WPINC')) {
    die;
}

// 定义插件常量
define('WP_TELEGRAM_INTEGRATION_VERSION', '1.0.0');
define('WP_TELEGRAM_INTEGRATION_PATH', plugin_dir_path(__FILE__));
define('WP_TELEGRAM_INTEGRATION_URL', plugin_dir_url(__FILE__));

// 加载模块文件
require_once WP_TELEGRAM_INTEGRATION_PATH . 'modules/core.php';
require_once WP_TELEGRAM_INTEGRATION_PATH . 'modules/image-fix.php';
require_once WP_TELEGRAM_INTEGRATION_PATH . 'modules/paid-content.php';
require_once WP_TELEGRAM_INTEGRATION_PATH . 'modules/proxy.php';
require_once WP_TELEGRAM_INTEGRATION_PATH . 'modules/queue.php';
require_once WP_TELEGRAM_INTEGRATION_PATH . 'modules/logger.php';

// 插件激活钩子
register_activation_hook(__FILE__, 'wp_telegram_integration_activate');

// 插件停用钩子
register_deactivation_hook(__FILE__, 'wp_telegram_integration_deactivate');

/**
 * 插件激活时执行
 */
function wp_telegram_integration_activate() {
    // 初始化设置
    add_option('wp_telegram_integration_bot_token', get_option('wp_telegram_sync_bot_token', ''));
    add_option('wp_telegram_integration_channel_id', get_option('wp_telegram_sync_channel_id', ''));
    add_option('wp_telegram_integration_message_template', get_option('wp_telegram_sync_message_template', "📝 *{post_title}*\n\n{post_excerpt}\n\n🔗 [阅读全文]({post_url})"));
    add_option('wp_telegram_integration_proxy', get_option('wp_telegram_socks5h_proxy', ''));
    add_option('wp_telegram_integration_log_level', 'error'); // 默认仅记录错误

    // 设置定时任务
    if (!wp_next_scheduled('wp_telegram_integration_queue_cron')) {
        wp_schedule_event(time(), 'minute', 'wp_telegram_integration_queue_cron');
    }
    
    // 记录激活日志
    wp_telegram_log('插件已激活', 'info');
}

/**
 * 插件停用时执行
 */
function wp_telegram_integration_deactivate() {
    // 清理定时任务
    $timestamp = wp_next_scheduled('wp_telegram_integration_queue_cron');
    if ($timestamp) {
        wp_unschedule_event($timestamp, 'wp_telegram_integration_queue_cron');
    }
    
    // 记录停用日志
    wp_telegram_log('插件已停用', 'info');
}

/**
 * 在已有的Cron间隔中添加"每分钟"选项
 */
function wp_telegram_integration_cron_schedules($schedules) {
    if (!isset($schedules['minute'])) {
        $schedules['minute'] = array(
            'interval' => 60,
            'display' => __('每分钟'),
        );
    }
    return $schedules;
}
add_filter('cron_schedules', 'wp_telegram_integration_cron_schedules');

/**
 * 添加设置菜单
 */
function wp_telegram_integration_menu() {
    add_options_page(
        'Telegram集成设置',
        'Telegram集成',
        'manage_options',
        'wp-telegram-integration',
        'wp_telegram_integration_settings_page'
    );
}
add_action('admin_menu', 'wp_telegram_integration_menu');

/**
 * 添加插件设置链接
 */
function wp_telegram_integration_plugin_action_links($links) {
    $settings_link = '<a href="' . admin_url('options-general.php?page=wp-telegram-integration') . '">设置</a>';
    array_unshift($links, $settings_link);
    return $links;
}
add_filter('plugin_action_links_' . plugin_basename(__FILE__), 'wp_telegram_integration_plugin_action_links');

/**
 * 设置页面
 */
function wp_telegram_integration_settings_page() {
    // 检查权限
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 处理表单提交
    if (isset($_POST['wp_telegram_integration_settings']) && check_admin_referer('wp_telegram_integration_settings', 'wp_telegram_integration_nonce')) {
        // 保存Bot Token
        if (isset($_POST['bot_token'])) {
            update_option('wp_telegram_integration_bot_token', sanitize_text_field($_POST['bot_token']));
        }
        
        // 保存频道ID
        if (isset($_POST['channel_id'])) {
            update_option('wp_telegram_integration_channel_id', sanitize_text_field($_POST['channel_id']));
        }
        
        // 保存消息模板
        if (isset($_POST['message_template'])) {
            update_option('wp_telegram_integration_message_template', wp_kses_post($_POST['message_template']));
        }
        
        // 保存代理设置
        if (isset($_POST['proxy'])) {
            update_option('wp_telegram_integration_proxy', sanitize_text_field($_POST['proxy']));
        }
        
        // 保存日志级别
        if (isset($_POST['log_level'])) {
            update_option('wp_telegram_integration_log_level', sanitize_text_field($_POST['log_level']));
        }
        
        // 显示成功消息
        echo '<div class="notice notice-success is-dismissible"><p>设置已保存。</p></div>';
    }
    
    // 获取当前设置
    $bot_token = get_option('wp_telegram_integration_bot_token', '');
    $channel_id = get_option('wp_telegram_integration_channel_id', '');
    $message_template = get_option('wp_telegram_integration_message_template', "📝 *{post_title}*\n\n{post_excerpt}\n\n🔗 [阅读全文]({post_url})");
    $proxy = get_option('wp_telegram_integration_proxy', '');
    $log_level = get_option('wp_telegram_integration_log_level', 'error');
    
    // 输出设置表单
    ?>
    <div class="wrap">
        <h1>WordPress Telegram 集成设置</h1>
        
        <form method="post" action="">
            <?php wp_nonce_field('wp_telegram_integration_settings', 'wp_telegram_integration_nonce'); ?>
            <input type="hidden" name="wp_telegram_integration_settings" value="1">
            
            <table class="form-table" role="presentation">
                <tbody>
                    <tr>
                        <th scope="row">
                            <label for="bot_token">Telegram Bot Token</label>
                        </th>
                        <td>
                            <input name="bot_token" type="text" id="bot_token" value="<?php echo esc_attr($bot_token); ?>" class="regular-text">
                            <p class="description">从BotFather获取的API Token</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="channel_id">Telegram频道ID</label>
                        </th>
                        <td>
                            <input name="channel_id" type="text" id="channel_id" value="<?php echo esc_attr($channel_id); ?>" class="regular-text">
                            <p class="description">格式为: @channel_name 或 -100xxxxxxxxxx</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="message_template">消息模板</label>
                        </th>
                        <td>
                            <textarea name="message_template" id="message_template" rows="5" class="large-text"><?php echo esc_textarea($message_template); ?></textarea>
                            <p class="description">
                                可用变量: {post_title}, {post_excerpt}, {post_content}, {post_url}, {post_author}, {post_date}
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="proxy">代理设置</label>
                        </th>
                        <td>
                            <input name="proxy" type="text" id="proxy" value="<?php echo esc_attr($proxy); ?>" class="regular-text">
                            <p class="description">格式: socks5h://127.0.0.1:7890</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="log_level">日志级别</label>
                        </th>
                        <td>
                            <select name="log_level" id="log_level">
                                <option value="debug" <?php selected($log_level, 'debug'); ?>>调试 (详细)</option>
                                <option value="info" <?php selected($log_level, 'info'); ?>>信息</option>
                                <option value="warning" <?php selected($log_level, 'warning'); ?>>警告</option>
                                <option value="error" <?php selected($log_level, 'error'); ?>>错误 (默认)</option>
                                <option value="critical" <?php selected($log_level, 'critical'); ?>>严重错误</option>
                            </select>
                            <p class="description">选择记录的日志级别 (选择级别越低记录越详细)</p>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <?php submit_button('保存设置'); ?>
        </form>
        
        <hr>
        
        <!-- 测试功能区 -->
        <h2>测试与工具</h2>
        
        <!-- 测试Telegram连接 -->
        <form method="post" action="">
            <?php wp_nonce_field('wp_telegram_integration_test_connection', 'wp_telegram_integration_test_connection_nonce'); ?>
            <input type="hidden" name="wp_telegram_integration_test_connection" value="1">
            <?php submit_button('测试Telegram API连接', 'secondary', 'test_connection', false); ?>
        </form>
        
        <!-- 发送测试消息 -->
        <form method="post" action="" style="margin-top: 10px;">
            <?php wp_nonce_field('wp_telegram_integration_test_message', 'wp_telegram_integration_test_message_nonce'); ?>
            <input type="hidden" name="wp_telegram_integration_test_message" value="1">
            <?php submit_button('发送测试消息', 'secondary', 'test_message', false); ?>
        </form>
        
        <!-- 手动同步队列 -->
        <form method="post" action="" style="margin-top: 10px;">
            <?php wp_nonce_field('wp_telegram_integration_process_queue', 'wp_telegram_integration_process_queue_nonce'); ?>
            <input type="hidden" name="wp_telegram_integration_process_queue" value="1">
            <?php submit_button('手动处理队列', 'secondary', 'process_queue', false); ?>
        </form>
        
        <div style="margin-top: 20px;">
            <h3>状态信息</h3>
            <?php
            // 显示队列长度
            $queue = get_option('wp_telegram_integration_queue', array());
            echo '<p>当前队列中有 <strong>' . count($queue) . '</strong> 个待处理项</p>';
            
            // 显示下一次定时任务时间
            $next_cron = wp_next_scheduled('wp_telegram_integration_queue_cron');
            if ($next_cron) {
                echo '<p>下一次自动处理计划在: <strong>' . date('Y-m-d H:i:s', $next_cron) . '</strong></p>';
            } else {
                echo '<p>没有计划的自动处理任务</p>';
            }
            ?>
        </div>
        
        <?php
        // 处理测试连接请求
        if (isset($_POST['wp_telegram_integration_test_connection']) && check_admin_referer('wp_telegram_integration_test_connection', 'wp_telegram_integration_test_connection_nonce')) {
            // 调用测试连接函数
            $result = wp_telegram_test_connection();
            if (is_wp_error($result)) {
                echo '<div class="notice notice-error is-dismissible"><p>连接测试失败: ' . $result->get_error_message() . '</p></div>';
            } else {
                echo '<div class="notice notice-success is-dismissible"><p>连接测试成功: ' . $result . '</p></div>';
            }
        }
        
        // 处理测试消息请求
        if (isset($_POST['wp_telegram_integration_test_message']) && check_admin_referer('wp_telegram_integration_test_message', 'wp_telegram_integration_test_message_nonce')) {
            // 调用发送测试消息函数
            $result = wp_telegram_send_test_message();
            if (is_wp_error($result)) {
                echo '<div class="notice notice-error is-dismissible"><p>测试消息发送失败: ' . $result->get_error_message() . '</p></div>';
            } else {
                echo '<div class="notice notice-success is-dismissible"><p>测试消息发送成功！</p></div>';
            }
        }
        
        // 处理手动处理队列请求
        if (isset($_POST['wp_telegram_integration_process_queue']) && check_admin_referer('wp_telegram_integration_process_queue', 'wp_telegram_integration_process_queue_nonce')) {
            // 处理队列
            $processed = wp_telegram_process_queue_manually();
            echo '<div class="notice notice-info is-dismissible"><p>手动处理了 ' . $processed . ' 个队列项</p></div>';
        }
        ?>
    </div>
    <?php
}

// 处理原有插件的函数
// 这些函数会被模块中的函数替代，这里只是为了兼容性而添加
if (!function_exists('wp_telegram_sync_publish_post')) {
    function wp_telegram_sync_publish_post($post_id, $post, $update) {
        return wp_telegram_publish_post($post_id, $post, $update);
    }
} 