=== WP Telegram Integration ===
Contributors: Claude
Tags: telegram, social media, notification, sync, proxy, socks5, bot
Requires at least: 5.0
Tested up to: 6.3
Stable tag: 1.0.0
Requires PHP: 7.0
License: GPLv2 or later

一个全功能的WordPress与Telegram集成插件，支持文章同步、图片修复、付费内容标记和代理设置。

== 描述 ==

WP Telegram Integration是一个全面的WordPress与Telegram集成工具，将原有的多个插件功能整合为一体，提供更稳定、更高效的文章同步体验。

主要功能：

* 将WordPress文章自动发送到Telegram频道或群组
* 支持文章图片和付费内容处理
* 内置队列系统，保证消息发送可靠性
* 完善的SOCKS5代理支持，解决国内访问问题
* 详细的日志记录和队列管理工具

== 安装 ==

1. 上传插件文件夹到`/wp-content/plugins/`目录
2. 在WordPress后台激活插件
3. 前往"设置" > "Telegram集成"配置插件

== 配置 ==

1. **Bot Token**：从BotFather获取的机器人API Token
2. **频道ID**：目标Telegram频道或群组的ID（格式：@channel_name 或 -100xxxxxxxxxx）
3. **消息模板**：自定义消息发送格式，支持多种变量
4. **代理设置**：配置SOCKS5代理以解决连接问题（格式：socks5://127.0.0.1:7890）

== 常见问题 ==

= 如何创建Telegram Bot? =

1. 在Telegram中搜索@BotFather并启动对话
2. 发送 /newbot 命令
3. 按照提示设置Bot名称和用户名
4. 获取生成的API Token

= 如何获取频道ID? =

* 对于公开频道：直接使用 @channel_name 格式
* 对于私有频道：将Bot添加为频道管理员，发送一条消息，然后通过API获取ID

= 消息发送失败怎么办? =

1. 检查Bot Token和频道ID是否正确
2. 检查代理设置是否可用，测试连接
3. 查看"Telegram日志"了解详细错误信息
4. 使用"Telegram队列"手动重试失败的消息

== 更新日志 ==

= 1.0.0 =
* 首次发布
* 整合原有的wp-telegram-sync、wp-telegram-sync-image-fix、wp-telegram-sync-paid-fix和wp-telegram-socks5h-fix插件
* 添加队列管理和日志系统
* 优化代理支持
* 改进图片处理 