<?php
if( !defined('ABSPATH') ){ exit();}

/**
 * 推文队列管理页面
 */
function xyz_twap_queue_page() {
    global $wpdb;
    
    // 处理操作
    if (isset($_POST['action']) && $_POST['action'] == 'process_queue') {
        // 手动处理队列
        $processed = xyz_twap_process_queue();
        echo '<div class="updated"><p>已处理 ' . $processed . ' 条推文</p></div>';
    }
    
    if (isset($_POST['action']) && $_POST['action'] == 'clear_queue') {
        // 清空队列
        update_option('xyz_twap_queue', array());
        echo '<div class="updated"><p>推文队列已清空</p></div>';
    }
    
    if (isset($_GET['reset']) && is_numeric($_GET['reset'])) {
        // 重置失败状态
        $post_id = intval($_GET['reset']);
        delete_post_meta($post_id, 'xyz_twap_failed');
        delete_post_meta($post_id, 'xyz_twap_retry_count');
        echo '<div class="updated"><p>已重置文章状态</p></div>';
    }
    
    // 删除队列项
    if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
        $post_id = intval($_GET['delete']);
        if (xyz_twap_remove_from_queue($post_id)) {
            echo '<div class="updated"><p>已从队列中删除文章</p></div>';
        }
    }
    
    // 优先发布队列项
    if (isset($_GET['priority']) && is_numeric($_GET['priority'])) {
        $post_id = intval($_GET['priority']);
        if (xyz_twap_prioritize_queue_item($post_id)) {
            echo '<div class="updated"><p>文章已设为优先发布</p></div>';
        }
    }
    
    // 立即发布队列项
    if (isset($_GET['publish_now']) && is_numeric($_GET['publish_now'])) {
        $post_id = intval($_GET['publish_now']);
        
        // 检查发布频率限制
        if (xyz_twap_check_rate_limit()) {
            // 从队列中移除
            xyz_twap_remove_from_queue($post_id);
            
            // 直接发布
            $result = xyz_twap_direct_publish($post_id);
            if ($result) {
                echo '<div class="updated"><p>文章已成功发布到Twitter</p></div>';
                // 记录到历史
                xyz_twap_add_to_history($post_id, 'success', '手动立即发布');
            } else {
                echo '<div class="error"><p>发布失败，请查看日志</p></div>';
                // 记录到历史
                xyz_twap_add_to_history($post_id, 'failed', '手动发布失败');
            }
        } else {
            echo '<div class="error"><p>发布频率受限，请稍后再试</p></div>';
        }
    }
    
    // 添加文章到队列（用于测试）
    if (isset($_POST['action']) && $_POST['action'] == 'add_to_queue' && !empty($_POST['post_id'])) {
        $post_id = intval($_POST['post_id']);
        xyz_twap_add_to_queue($post_id);
        echo '<div class="updated"><p>文章已添加到队列</p></div>';
    }
    
    // 添加历史项目到队列
    if (isset($_GET['add_to_queue']) && is_numeric($_GET['add_to_queue'])) {
        $post_id = intval($_GET['add_to_queue']);
        xyz_twap_add_to_queue($post_id);
        echo '<div class="updated"><p>文章已重新添加到队列</p></div>';
    }
    
    // 获取当前队列
    $queue = get_option('xyz_twap_queue', array());
    $queue_count = count($queue);
    
    // 获取发布历史记录
    $history = get_option('xyz_twap_history', array());
    
    // 排序历史记录，最新的在前面
    if (!empty($history)) {
        usort($history, function($a, $b) {
            return $b['time'] - $a['time'];
        });
    }
    
    // 获取定时任务信息
    $next_run = wp_next_scheduled('xyz_twap_process_queue');
    
    ?>
    <div class="wrap">
        <h1>推文队列管理</h1>
        
        <h2>队列状态</h2>
        <p>当前队列中有 <strong><?php echo $queue_count; ?></strong> 条待发布推文</p>
        <p>下次自动处理时间：<?php echo $next_run ? date('Y-m-d H:i:s', $next_run) : '未计划'; ?></p>
        
        <form method="post" action="">
            <input type="hidden" name="action" value="process_queue">
            <?php wp_nonce_field('xyz_twap_process_queue'); ?>
            <p class="submit">
                <input type="submit" class="button button-primary" value="手动处理队列">
                <button type="submit" class="button" name="action" value="clear_queue" onclick="return confirm('确定要清空队列吗？');">清空队列</button>
            </p>
        </form>
        
        <h2>添加文章到队列</h2>
        <form method="post" action="">
            <input type="hidden" name="action" value="add_to_queue">
            <?php wp_nonce_field('xyz_twap_add_to_queue'); ?>
            <p>
                <label for="post_id">文章ID：</label>
                <input type="number" name="post_id" id="post_id" required>
                <input type="submit" class="button" value="添加到队列">
            </p>
        </form>
        
        <?php if (!empty($queue)): ?>
        <h2>当前队列</h2>
        <p>提示：您可以点击"优先发布"将文章移到队列最前面，点击"立即发布"直接发布到Twitter，或点击"删除"从队列中移除文章。</p>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>标题</th>
                    <th>添加时间</th>
                    <th>重试次数</th>
                    <th>优先级</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($queue as $item): 
                    $post = get_post($item['post_id']);
                    if (!$post) continue;
                    $retry_count = get_post_meta($item['post_id'], 'xyz_twap_retry_count', true);
                    $priority = isset($item['priority']) ? $item['priority'] : 0;
                ?>
                <tr>
                    <td><?php echo $item['post_id']; ?></td>
                    <td><a href="<?php echo get_edit_post_link($item['post_id']); ?>" target="_blank"><?php echo $post->post_title; ?></a></td>
                    <td><?php echo date('Y-m-d H:i:s', $item['added']); ?></td>
                    <td><?php echo $retry_count ? $retry_count : '0'; ?></td>
                    <td><?php echo $priority ? '<span style="color:green;font-weight:bold;">优先</span>' : '普通'; ?></td>
                    <td>
                        <?php if (!$priority): ?>
                        <a href="?page=twitter-auto-publish-queue&priority=<?php echo $item['post_id']; ?>" class="button button-small">优先发布</a>
                        <?php endif; ?>
                        <a href="?page=twitter-auto-publish-queue&publish_now=<?php echo $item['post_id']; ?>" class="button button-small button-primary" onclick="return confirm('确定要立即发布此文章到Twitter吗？');">立即发布</a>
                        <a href="?page=twitter-auto-publish-queue&delete=<?php echo $item['post_id']; ?>" class="button button-small" style="color:red;" onclick="return confirm('确定要从队列中删除此文章吗？');">删除</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
        
        <?php if (!empty($history)): ?>
        <h2>发布历史（最近30条）</h2>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>标题</th>
                    <th>发布时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $count = 0;
                foreach ($history as $item): 
                    $post = get_post($item['post_id']);
                    if (!$post) continue;
                    $count++;
                    if ($count > 30) break; // 只显示最近30条
                ?>
                <tr>
                    <td><?php echo $item['post_id']; ?></td>
                    <td><a href="<?php echo get_edit_post_link($item['post_id']); ?>" target="_blank"><?php echo $post->post_title; ?></a></td>
                    <td><?php echo date('Y-m-d H:i:s', $item['time']); ?></td>
                    <td>
                        <?php 
                        if ($item['status'] === 'success') {
                            echo '<span style="color:green;">成功</span>';
                        } else {
                            echo '<span style="color:red;">失败：' . esc_html($item['message']) . '</span>';
                        }
                        ?>
                    </td>
                    <td>
                        <?php if ($item['status'] === 'failed'): ?>
                        <a href="?page=twitter-auto-publish-queue&reset=<?php echo $item['post_id']; ?>" class="button">重置</a>
                        <?php endif; ?>
                        <a href="?page=twitter-auto-publish-queue&add_to_queue=<?php echo $item['post_id']; ?>" class="button button-small">重新加入队列</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * 添加文章到推文队列
 * 
 * @param int $post_id 文章ID
 * @param int $priority 优先级（1为优先，0为普通）
 * @return bool 是否成功添加
 */
function xyz_twap_add_to_queue($post_id, $priority = 0) {
    $queue = get_option('xyz_twap_queue', array());
    
    // 检查是否已在队列中
    foreach ($queue as $key => $item) {
        if ($item['post_id'] == $post_id) {
            // 如果要设为优先级，则更新
            if ($priority) {
                $queue[$key]['priority'] = 1;
                update_option('xyz_twap_queue', $queue);
                return true;
            }
            return false;
        }
    }
    
    // 添加到队列
    $new_item = array(
        'post_id' => $post_id,
        'added' => time(),
        'priority' => $priority
    );
    
    // 如果是优先项，添加到队列前面
    if ($priority) {
        array_unshift($queue, $new_item);
    } else {
        $queue[] = $new_item;
    }
    
    update_option('xyz_twap_queue', $queue);
    
    // 重置重试次数
    delete_post_meta($post_id, 'xyz_twap_retry_count');
    delete_post_meta($post_id, 'xyz_twap_failed');
    
    // 确保定时任务已设置
    if (!wp_next_scheduled('xyz_twap_process_queue')) {
        wp_schedule_event(time() + 60, 'twap_minute', 'xyz_twap_process_queue');
    }
    
    return true;
}

/**
 * 设置队列项为优先发布
 * 
 * @param int $post_id 文章ID
 * @return bool 是否成功设置
 */
function xyz_twap_prioritize_queue_item($post_id) {
    $queue = get_option('xyz_twap_queue', array());
    
    // 首先从队列中移除该项
    foreach ($queue as $key => $item) {
        if ($item['post_id'] == $post_id) {
            unset($queue[$key]);
            break;
        }
    }
    
    // 重新索引队列
    $queue = array_values($queue);
    
    // 将该项添加到队列前面，并设置优先级
    array_unshift($queue, array(
        'post_id' => $post_id,
        'added' => time(),
        'priority' => 1
    ));
    
    update_option('xyz_twap_queue', $queue);
    return true;
}

/**
 * 直接发布文章到Twitter（绕过队列）
 * 
 * @param int $post_id 文章ID
 * @return bool 是否成功发布
 */
function xyz_twap_direct_publish($post_id) {
    // 临时禁用队列模式
    $queue_enabled = get_option('xyz_twap_queue_enabled', 1);
    update_option('xyz_twap_queue_enabled', 0);
    
    // 直接发布
    require_once(dirname(__FILE__) . '/publish.php');
    $result = xyz_twap_link_publish($post_id);
    
    // 恢复队列设置
    update_option('xyz_twap_queue_enabled', $queue_enabled);
    
    return $result;
}

/**
 * 从队列移除文章
 * 
 * @param int $post_id 文章ID
 * @return bool 是否成功移除
 */
function xyz_twap_remove_from_queue($post_id) {
    $queue = get_option('xyz_twap_queue', array());
    $updated = false;
    
    foreach ($queue as $key => $item) {
        if ($item['post_id'] == $post_id) {
            unset($queue[$key]);
            $updated = true;
        }
    }
    
    if ($updated) {
        $queue = array_values($queue); // 重新索引数组
        update_option('xyz_twap_queue', $queue);
        
        // 如果队列为空，取消定时任务
        if (empty($queue)) {
            $timestamp = wp_next_scheduled('xyz_twap_process_queue');
            if ($timestamp) {
                wp_unschedule_event($timestamp, 'xyz_twap_process_queue');
            }
        }
    }
    
    return $updated;
}

/**
 * 处理推文队列
 * 
 * @param int $limit 处理的最大项数，默认为1
 * @return int 成功处理的项数
 */
function xyz_twap_process_queue($limit = 1) {
    $queue = get_option('xyz_twap_queue', array());
    if (empty($queue)) {
        return 0;
    }
    
    // 根据优先级对队列排序
    usort($queue, function($a, $b) {
        $a_priority = isset($a['priority']) ? $a['priority'] : 0;
        $b_priority = isset($b['priority']) ? $b['priority'] : 0;
        
        if ($a_priority == $b_priority) {
            // 如果优先级相同，按添加时间排序（早的先发）
            return $a['added'] - $b['added'];
        }
        
        // 优先级高的先发
        return $b_priority - $a_priority;
    });
    
    // 更新排序后的队列
    update_option('xyz_twap_queue', $queue);
    
    $processed = 0;
    $remaining = array();
    
    foreach ($queue as $item) {
        if ($processed >= $limit) {
            $remaining[] = $item;
            continue;
        }
        
        $post_id = $item['post_id'];
        $retry_count = (int) get_post_meta($post_id, 'xyz_twap_retry_count', true);
        
        // 检查重试次数是否超过限制
        $max_retries = 5; // 最大重试次数
        if ($retry_count >= $max_retries) {
            // 记录到历史
            xyz_twap_add_to_history($post_id, 'failed', '超过最大重试次数');
            update_post_meta($post_id, 'xyz_twap_failed', true);
            continue;
        }
        
        // 检查发布间隔
        if (!xyz_twap_check_rate_limit()) {
            // 如果不符合间隔要求，保留在队列中
            $remaining[] = $item;
            continue;
        }
        
        // 获取文章
        $post = get_post($post_id);
        if (!$post || $post->post_status != 'publish') {
            // 记录到历史
            xyz_twap_add_to_history($post_id, 'failed', '文章不存在或未发布');
            continue;
        }
        
        // 尝试发布
        $result = xyz_twap_direct_publish($post_id);
        
        if ($result === true) {
            // 记录成功
            xyz_twap_add_to_history($post_id, 'success');
            $processed++;
        } else {
            // 增加重试次数
            $retry_count++;
            update_post_meta($post_id, 'xyz_twap_retry_count', $retry_count);
            
            // 添加回队列尾部
            $remaining[] = $item;
            
            // 记录到历史
            xyz_twap_add_to_history($post_id, 'failed', '发布失败，已增加重试次数');
        }
    }
    
    // 更新队列
    update_option('xyz_twap_queue', $remaining);
    
    // 如果队列为空，取消定时任务
    if (empty($remaining)) {
        $timestamp = wp_next_scheduled('xyz_twap_process_queue');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'xyz_twap_process_queue');
        }
    }
    
    return $processed;
}

/**
 * 添加记录到历史
 * 
 * @param int $post_id 文章ID
 * @param string $status 状态（success/failed）
 * @param string $message 消息（失败原因）
 */
function xyz_twap_add_to_history($post_id, $status, $message = '') {
    $history = get_option('xyz_twap_history', array());
    
    // 限制历史记录数量
    if (count($history) > 100) {
        array_shift($history);
    }
    
    // 添加新记录
    $history[] = array(
        'post_id' => $post_id,
        'time' => time(),
        'status' => $status,
        'message' => $message
    );
    
    update_option('xyz_twap_history', $history);
}

/**
 * 定时处理队列
 */
function xyz_twap_cron_process_queue() {
    xyz_twap_process_queue(1); // 每次只处理一条
}
add_action('xyz_twap_process_queue', 'xyz_twap_cron_process_queue');

// 添加自定义计划间隔
function xyz_twap_add_cron_intervals($schedules) {
    $schedules['twap_minute'] = array(
        'interval' => 60, // 1分钟
        'display' => '每分钟'
    );
    return $schedules;
}
add_filter('cron_schedules', 'xyz_twap_add_cron_intervals'); 