<?php
/**
 * 代理处理模块
 * 
 * 提供SOCKS5代理支持和代理管理功能
 */

// 禁止直接访问
if (!defined('WPINC')) {
    die;
}

/**
 * 给HTTP请求添加代理设置
 * 
 * @param array $args 请求参数
 * @param string $url 请求URL
 * @return array 应用代理后的请求参数
 */
function wp_telegram_proxy_http_request($args, $url) {
    // 只对Telegram API请求应用代理
    if (strpos($url, 'api.telegram.org') !== false) {
        $proxy = get_option('wp_telegram_integration_proxy', '');
        
        // 如果插件设置中没有配置代理，使用默认代理
        if (empty($proxy)) {
            $proxy = 'socks5://************:7890';
            wp_telegram_log('使用默认SOCKS5代理: ' . $proxy . ' 访问: ' . $url, 'debug');
        }
        
        if (!empty($proxy)) {
            $args['proxy'] = $proxy;
            wp_telegram_log('应用代理: ' . $proxy . ' 访问: ' . $url, 'debug');
        }
        
        // 增加超时时间
        $args['timeout'] = 60; // 增加到60秒
    }
    
    return $args;
}
add_filter('http_request_args', 'wp_telegram_proxy_http_request', 10, 2);

/**
 * 给cURL请求添加SOCKS5代理设置
 * 
 * @param resource $handle cURL句柄
 * @param array $args 请求参数
 * @param string $url 请求URL
 * @return resource 设置代理后的cURL句柄
 */
function wp_telegram_proxy_curl_handle($handle, $args, $url) {
    // 只对Telegram API请求应用代理
    if (strpos($url, 'api.telegram.org') !== false) {
        // 获取代理设置
        $proxy = '';
        
        // 如果请求参数中已经包含代理设置
        if (isset($args['proxy']) && !empty($args['proxy'])) {
            $proxy = $args['proxy'];
        } else {
            // 尝试从插件设置获取代理
            $proxy = get_option('wp_telegram_integration_proxy', '');
            
            // 如果插件设置中没有配置代理，使用默认代理
            if (empty($proxy)) {
                $proxy = 'socks5://************:7890';
            }
        }
        
        // 应用代理设置
        if (!empty($proxy)) {
            // 如果是SOCKS5代理
            if (strpos($proxy, 'socks5') === 0) {
                curl_setopt($handle, CURLOPT_PROXYTYPE, 7); // CURLPROXY_SOCKS5_HOSTNAME = 7
                
                // 去掉socks5://前缀
                $proxy_address = str_replace(array('socks5://', 'socks5h://'), '', $proxy);
                
                // 分割主机和端口
                $parts = explode(':', $proxy_address);
                if (count($parts) === 2) {
                    curl_setopt($handle, CURLOPT_PROXY, $parts[0]);
                    curl_setopt($handle, CURLOPT_PROXYPORT, $parts[1]);
                    
                    wp_telegram_log('设置cURL SOCKS5代理: ' . $proxy . ' (' . $parts[0] . ':' . $parts[1] . ')', 'debug');
                } else {
                    curl_setopt($handle, CURLOPT_PROXY, $proxy_address);
                    wp_telegram_log('设置cURL SOCKS5代理: ' . $proxy . ' (无端口)', 'debug');
                }
                
                // 禁用IPv6解析，强制使用IPv4
                curl_setopt($handle, CURLOPT_IPRESOLVE, 1); // CURL_IPRESOLVE_V4 = 1
            } else {
                // HTTP代理
                curl_setopt($handle, CURLOPT_PROXY, $proxy);
                wp_telegram_log('设置cURL HTTP代理: ' . $proxy, 'debug');
            }
            
            // 增加超时时间
            curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 30);
            curl_setopt($handle, CURLOPT_TIMEOUT, 60);
        }
    }
    
    return $handle;
}
add_action('http_api_curl', 'wp_telegram_proxy_curl_handle', 10, 3);

/**
 * 测试代理连接
 * 
 * @param string $proxy 代理地址
 * @return bool|WP_Error 测试结果
 */
function wp_telegram_test_proxy($proxy = '') {
    if (empty($proxy)) {
        $proxy = get_option('wp_telegram_integration_proxy', '');
    }
    
    if (empty($proxy)) {
        return new WP_Error('missing_proxy', '代理地址为空');
    }
    
    // 测试连接到Telegram API
    $url = 'https://api.telegram.org/';
    $args = array(
        'timeout' => 30,
        'redirection' => 5,
        'httpversion' => '1.0',
        'blocking' => true,
        'headers' => array(),
        'cookies' => array(),
        'proxy' => $proxy
    );
    
    wp_telegram_log('测试代理连接: ' . $proxy . ' 连接到: ' . $url, 'info');
    $response = wp_remote_get($url, $args);
    
    if (is_wp_error($response)) {
        wp_telegram_log('代理测试失败: ' . $response->get_error_message(), 'error');
        return $response;
    }
    
    $status_code = wp_remote_retrieve_response_code($response);
    
    if ($status_code >= 200 && $status_code < 300) {
        wp_telegram_log('代理测试成功，HTTP状态码: ' . $status_code, 'info');
        return true;
    } else {
        wp_telegram_log('代理测试失败，HTTP状态码: ' . $status_code, 'error');
        return new WP_Error('proxy_test_failed', '代理测试失败，HTTP状态码: ' . $status_code);
    }
}

/**
 * 添加代理设置字段
 */
function wp_telegram_proxy_settings() {
    add_settings_section(
        'wp_telegram_proxy_section',
        '代理设置',
        function() {
            echo '<p>配置用于访问Telegram API的代理服务器</p>';
        },
        'wp-telegram-integration'
    );
    
    add_settings_field(
        'wp_telegram_integration_proxy',
        '代理服务器',
        'wp_telegram_proxy_settings_callback',
        'wp-telegram-integration',
        'wp_telegram_proxy_section'
    );
    
    register_setting('wp_telegram_integration_options', 'wp_telegram_integration_proxy');
}
add_action('admin_init', 'wp_telegram_proxy_settings');

/**
 * 代理设置字段回调
 */
function wp_telegram_proxy_settings_callback() {
    $proxy = get_option('wp_telegram_integration_proxy', '');
    ?>
    <input type="text" name="wp_telegram_integration_proxy" value="<?php echo esc_attr($proxy); ?>" class="regular-text" />
    <p class="description">
        格式: socks5://127.0.0.1:7890<br>
        留空则使用WordPress全局代理设置（如在wp-config.php中设置的WP_PROXY_HOST和WP_PROXY_PORT）
    </p>
    <div style="margin-top: 10px;">
        <button type="button" id="test_proxy_btn" class="button button-secondary">测试代理连接</button>
        <span id="proxy_test_result" style="margin-left: 10px;"></span>
    </div>
    
    <!-- 添加JavaScript测试代码 -->
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        $('#test_proxy_btn').on('click', function() {
            var proxy = $('input[name="wp_telegram_integration_proxy"]').val();
            var result_span = $('#proxy_test_result');
            
            result_span.html('<span style="color:#777;">正在测试...</span>');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'wp_telegram_test_proxy',
                    proxy: proxy,
                    nonce: '<?php echo wp_create_nonce('wp_telegram_test_proxy'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        result_span.html('<span style="color:green;">✓ 连接成功!</span>');
                    } else {
                        result_span.html('<span style="color:red;">✗ 连接失败: ' + response.data + '</span>');
                    }
                },
                error: function() {
                    result_span.html('<span style="color:red;">✗ 请求失败，请检查网络连接</span>');
                }
            });
        });
    });
    </script>
    <?php
}

/**
 * 添加代理测试的AJAX处理函数
 */
function wp_telegram_test_proxy_ajax() {
    check_ajax_referer('wp_telegram_test_proxy', 'nonce');
    
    $proxy = isset($_POST['proxy']) ? sanitize_text_field($_POST['proxy']) : '';
    $result = wp_telegram_test_proxy($proxy);
    
    if (is_wp_error($result)) {
        wp_send_json_error($result->get_error_message());
    } else {
        wp_send_json_success('代理连接正常');
    }
}
add_action('wp_ajax_wp_telegram_test_proxy', 'wp_telegram_test_proxy_ajax');

/**
 * 显示全局代理设置信息
 */
function wp_telegram_display_global_proxy_info() {
    if (defined('WP_PROXY_HOST') && defined('WP_PROXY_PORT')) {
        $proxy_type = defined('WP_PROXY_SOCKS5') && WP_PROXY_SOCKS5 ? 'SOCKS5' : 'HTTP';
        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <strong>全局代理设置: </strong>
                <?php echo esc_html('在wp-config.php中检测到' . $proxy_type . '代理设置: ' . WP_PROXY_HOST . ':' . WP_PROXY_PORT); ?>
            </p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'wp_telegram_display_global_proxy_info');

/**
 * 测试Telegram API连接（用于在设置页面显示）
 */
function wp_telegram_test_telegram_api_button() {
    ?>
    <hr>
    <h3>代理连接测试</h3>
    <form method="post" action="">
        <?php wp_nonce_field('wp_telegram_test_api', 'wp_telegram_test_api_nonce'); ?>
        <input type="hidden" name="wp_telegram_test_api" value="1">
        <?php submit_button('测试Telegram API连接', 'secondary', 'test_api', false); ?>
    </form>
    
    <?php
    // 处理测试请求
    if (isset($_POST['wp_telegram_test_api']) && check_admin_referer('wp_telegram_test_api', 'wp_telegram_test_api_nonce')) {
        $proxy = get_option('wp_telegram_integration_proxy', '');
        
        if (!empty($proxy)) {
            echo '<p>使用代理: ' . esc_html($proxy) . '</p>';
        } else if (defined('WP_PROXY_HOST') && defined('WP_PROXY_PORT')) {
            $proxy_type = defined('WP_PROXY_SOCKS5') && WP_PROXY_SOCKS5 ? 'SOCKS5' : 'HTTP';
            echo '<p>使用全局' . esc_html($proxy_type) . '代理: ' . esc_html(WP_PROXY_HOST . ':' . WP_PROXY_PORT) . '</p>';
        } else {
            echo '<p>未配置代理，使用直接连接</p>';
        }
        
        // 测试连接到Telegram API的getMe接口
        $bot_token = get_option('wp_telegram_integration_bot_token', '');
        if (empty($bot_token)) {
            echo '<div class="notice notice-error"><p>请先配置Bot Token</p></div>';
            return;
        }
        
        $result = wp_telegram_test_connection();
        if (is_wp_error($result)) {
            echo '<div class="notice notice-error"><p>连接失败: ' . esc_html($result->get_error_message()) . '</p></div>';
        } else {
            echo '<div class="notice notice-success"><p>连接成功: ' . esc_html($result) . '</p></div>';
        }
    }
} 