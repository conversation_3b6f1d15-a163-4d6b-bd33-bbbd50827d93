<?php
// 使用更可靠的方法找到WordPress加载文件
$wp_load_paths = array(
    dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php',  // 标准路径
    '../../../../wp-load.php',                                     // 相对路径
    '../../../wp-load.php',                                        // 备选路径1
    '../../wp-load.php',                                           // 备选路径2
    '../wp-load.php',                                              // 备选路径3
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    echo "错误：无法加载WordPress环境，请将此脚本移动到插件目录中。";
    exit;
}

// 确保只有管理员可以运行此脚本
if (!current_user_can('manage_options')) {
    echo "权限不足，请以管理员身份登录。";
    exit;
}

echo "<h1>Twitter自动发布插件修复工具</h1>";

// 显示当前设置
$current_interval = get_option('xyz_twap_post_interval', 15);
echo "<p>当前发布间隔设置：{$current_interval} 分钟</p>";

// 检查最后发布时间
$last_post_time = get_option('xyz_twap_last_tweet_time', 0);
if ($last_post_time > 0) {
    echo "<p>最后发布时间：" . date('Y-m-d H:i:s', $last_post_time) . "</p>";
    echo "<p>距现在时间：" . round((time() - $last_post_time) / 60) . " 分钟</p>";
} else {
    echo "<p>尚无发布记录</p>";
}

// 处理表单提交
if (isset($_POST['action'])) {
    if ($_POST['action'] == 'update_interval') {
        $new_interval = intval($_POST['interval']);
        if ($new_interval >= 6) {
            update_option('xyz_twap_post_interval', $new_interval);
            echo "<p style='color:green;'>已更新发布间隔为: {$new_interval} 分钟</p>";
        } else {
            echo "<p style='color:red;'>发布间隔必须至少为6分钟</p>";
        }
    } elseif ($_POST['action'] == 'reset_last_time') {
        update_option('xyz_twap_last_tweet_time', 0);
        echo "<p style='color:green;'>已重置最后发布时间</p>";
    } elseif ($_POST['action'] == 'clear_log') {
        $log_file = WP_CONTENT_DIR . '/twitter-rate-limit.log';
        if (file_exists($log_file)) {
            file_put_contents($log_file, '');
            echo "<p style='color:green;'>已清空日志文件</p>";
        } else {
            echo "<p style='color:orange;'>日志文件不存在</p>";
        }
    } elseif ($_POST['action'] == 'check_cron') {
        // 检查并修复定时任务
        $timestamp = wp_next_scheduled('xyz_twap_process_queue');
        if ($timestamp) {
            echo "<p>定时任务已存在，计划执行时间：" . date('Y-m-d H:i:s', $timestamp) . "</p>";
        } else {
            wp_schedule_event(time() + 60, 'twap_minute', 'xyz_twap_process_queue');
            echo "<p style='color:green;'>已重新计划定时任务，将在1分钟后执行</p>";
        }
    }
}

// 显示表单
?>

<form method="post" action="">
    <h2>设置发布间隔</h2>
    <input type="hidden" name="action" value="update_interval">
    <p>
        <label>新的发布间隔（分钟）：</label>
        <input type="number" name="interval" value="<?php echo $current_interval; ?>" min="6">
        <input type="submit" value="更新间隔">
    </p>
</form>

<form method="post" action="">
    <h2>重置最后发布时间</h2>
    <p>如果你想立即发布队列中的内容，可以重置最后发布时间</p>
    <input type="hidden" name="action" value="reset_last_time">
    <p>
        <input type="submit" value="重置时间" onclick="return confirm('确定要重置最后发布时间吗？这将允许立即发布队列中的下一条推文。')">
    </p>
</form>

<form method="post" action="">
    <h2>清空日志文件</h2>
    <input type="hidden" name="action" value="clear_log">
    <p>
        <input type="submit" value="清空日志" onclick="return confirm('确定要清空日志文件吗？')">
    </p>
</form>

<form method="post" action="">
    <h2>检查/修复定时任务</h2>
    <input type="hidden" name="action" value="check_cron">
    <p>
        <input type="submit" value="检查定时任务">
    </p>
</form>

<p><a href="../../admin.php?page=twitter-auto-publish-queue">返回队列管理</a></p>
<p><a href="../../admin.php?page=twitter-auto-publish-settings">返回插件设置</a></p> 