<?php
/**
 * 核心功能模块
 * 
 * 提供基本的Telegram消息发送功能
 */

// 禁止直接访问
if (!defined('WPINC')) {
    die;
}

/**
 * 发布文章时发送到Telegram
 * 
 * @param int $post_id 文章ID
 * @param WP_Post $post 文章对象
 * @param bool $update 是否为更新
 * @return bool|WP_Error 是否发送成功
 */
function wp_telegram_publish_post($post_id, $post, $update) {
    wp_telegram_log('处理文章ID: ' . $post_id . ' 更新状态: ' . ($update ? 'true' : 'false'), 'debug');
    
    // 如果是更新文章且不是第一次发布，则不发送
    if ($update && get_post_meta($post_id, '_telegram_integration_sent', true)) {
        wp_telegram_log('文章ID ' . $post_id . ' 已经推送过，跳过', 'debug');
        return false;
    }
    
    // 只处理公开的文章类型
    if ($post->post_status != 'publish' || $post->post_type != 'post') {
        wp_telegram_log('文章ID ' . $post_id . ' 不是已发布状态或非文章类型，跳过', 'debug');
        return false;
    }
    
    // 获取设置
    $bot_token = get_option('wp_telegram_integration_bot_token', '');
    $channel_id = get_option('wp_telegram_integration_channel_id', '');
    $template = get_option('wp_telegram_integration_message_template', "📝 *{post_title}*\n\n{post_excerpt}\n\n🔗 [阅读全文]({post_url})");
    
    if (empty($bot_token) || empty($channel_id)) {
        wp_telegram_log('Bot Token或Channel ID未设置，跳过', 'warning');
        return new WP_Error('missing_config', '请先配置Bot Token和频道ID');
    }
    
    // 准备消息内容
    $post_url = get_permalink($post_id);
    $post_title = $post->post_title;
    $post_author = get_the_author_meta('display_name', $post->post_author);
    $post_date = get_the_date('', $post_id);
    
    // 获取摘要
    if (has_excerpt($post_id)) {
        $post_excerpt = get_the_excerpt($post);
    } else {
        $post_excerpt = wp_trim_words($post->post_content, 110, '...');
    }
    
    // 替换模板变量
    $message = str_replace(
        array('{post_title}', '{post_excerpt}', '{post_content}', '{post_url}', '{post_author}', '{post_date}'),
        array($post_title, $post_excerpt, $post->post_content, $post_url, $post_author, $post_date),
        $template
    );
    
    // 应用消息内容过滤器，处理网盘链接等额外内容
    $message = apply_filters('wp_telegram_message_content', $message, $post_id);
    
    // 提取正文第一张图片
    $image_url = wp_telegram_get_first_image_url($post->post_content);
    
    // 发送消息
    $result = null;
    if ($image_url) {
        wp_telegram_log('文章ID ' . $post_id . ' 使用图片方式发送，图片URL: ' . $image_url, 'debug');
        $result = wp_telegram_send_photo($bot_token, $channel_id, $image_url, $message);
    } else {
        wp_telegram_log('文章ID ' . $post_id . ' 使用纯文本方式发送', 'debug');
        $result = wp_telegram_send_message($bot_token, $channel_id, $message);
    }
    
    // 如果发送成功，标记文章为已发送
    if ($result === true) {
        update_post_meta($post_id, '_telegram_integration_sent', true);
        wp_telegram_log('文章ID ' . $post_id . ' 推送成功并已打标记', 'info');
        return true;
    } else {
        wp_telegram_log('文章ID ' . $post_id . ' 推送失败: ' . ($result instanceof WP_Error ? $result->get_error_message() : '未知错误'), 'error');
        
        // 添加到队列以便稍后重试
        wp_telegram_add_to_queue($post_id);
        
        return $result;
    }
}

/**
 * 提取文章中第一张图片的URL
 * 
 * @param string $content 文章内容
 * @return string|bool 图片URL或false
 */
function wp_telegram_get_first_image_url($content) {
    if (preg_match('/<img.+src=[\'\"]([^\'\"]+)[\'\"].*>/i', $content, $matches)) {
        $image_url = $matches[1];
        
        // 处理相对协议URL（以//开头的URL）
        if (strpos($image_url, '//') === 0) {
            $image_url = 'https:' . $image_url;
            wp_telegram_log('检测到协议相对URL，添加https协议: ' . $image_url, 'debug');
        }
        // 处理其他相对URL
        else if (strpos($image_url, 'http') !== 0) {
            $site_domain = site_url();
            
            if (strpos($image_url, '/') === 0) {
                $image_url = $site_domain . $image_url;
            } else {
                $image_url = $site_domain . '/' . $image_url;
            }
            wp_telegram_log('检测到相对URL，添加域名: ' . $image_url, 'debug');
        }
        
        wp_telegram_log('找到图片URL: ' . $image_url, 'debug');
        return $image_url;
    }
    
    wp_telegram_log('未找到图片URL', 'debug');
    return false;
}

/**
 * 发送消息到Telegram
 * 
 * @param string $bot_token Bot Token
 * @param string $channel_id 频道ID
 * @param string $message 消息内容
 * @param bool $disable_web_preview 是否禁用网页预览
 * @return bool|WP_Error 是否发送成功
 */
function wp_telegram_send_message($bot_token, $channel_id, $message, $disable_web_preview = false) {
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    
    $args = array(
        'body' => array(
            'chat_id' => $channel_id,
            'text' => $message,
            'parse_mode' => 'Markdown',
            'disable_web_page_preview' => $disable_web_preview
        ),
        'timeout' => 30,
        'redirection' => 5,
        'httpversion' => '1.0',
        'blocking' => true,
        'headers' => array(),
        'cookies' => array()
    );
    
    // 应用代理设置
    $args = wp_telegram_apply_proxy($args);
    
    wp_telegram_log('发送消息到频道: ' . $channel_id, 'debug');
    $response = wp_remote_post($url, $args);
    
    if (is_wp_error($response)) {
        wp_telegram_log('发送失败: ' . $response->get_error_message(), 'error');
        return $response;
    }
    
    $body = json_decode(wp_remote_retrieve_body($response), true);
    $status_code = wp_remote_retrieve_response_code($response);
    
    if ($status_code !== 200 || !isset($body['ok']) || $body['ok'] !== true) {
        $error_message = isset($body['description']) ? $body['description'] : '未知错误 (HTTP ' . $status_code . ')';
        wp_telegram_log('API错误: ' . $error_message, 'error');
        return new WP_Error('telegram_api_error', $error_message);
    }
    
    wp_telegram_log('消息发送成功', 'debug');
    return true;
}

/**
 * 发送图片到Telegram
 * 
 * @param string $bot_token Bot Token
 * @param string $channel_id 频道ID
 * @param string $photo_url 图片URL
 * @param string $caption 图片说明
 * @return bool|WP_Error 是否发送成功
 */
function wp_telegram_send_photo($bot_token, $channel_id, $photo_url, $caption = '') {
    $url = "https://api.telegram.org/bot{$bot_token}/sendPhoto";
    
    $args = array(
        'body' => array(
            'chat_id' => $channel_id,
            'photo' => $photo_url,
            'caption' => $caption,
            'parse_mode' => 'Markdown',
        ),
        'timeout' => 30,
        'redirection' => 5,
        'httpversion' => '1.0',
        'blocking' => true,
        'headers' => array(),
        'cookies' => array()
    );
    
    // 应用代理设置
    $args = wp_telegram_apply_proxy($args);
    
    wp_telegram_log('发送图片到频道: ' . $channel_id . ', 图片URL: ' . $photo_url, 'debug');
    $response = wp_remote_post($url, $args);
    
    if (is_wp_error($response)) {
        wp_telegram_log('图片发送失败: ' . $response->get_error_message(), 'error');
        return $response;
    }
    
    $body = json_decode(wp_remote_retrieve_body($response), true);
    $status_code = wp_remote_retrieve_response_code($response);
    
    if ($status_code !== 200 || !isset($body['ok']) || $body['ok'] !== true) {
        $error_message = isset($body['description']) ? $body['description'] : '未知错误 (HTTP ' . $status_code . ')';
        wp_telegram_log('API错误: ' . $error_message, 'error');
        
        // 如果图片发送失败，尝试回退到纯文本模式
        wp_telegram_log('图片发送失败，尝试使用纯文本模式', 'warning');
        return wp_telegram_send_message($bot_token, $channel_id, $caption);
    }
    
    wp_telegram_log('图片发送成功', 'debug');
    return true;
}

/**
 * 发送测试消息
 * 
 * @return bool|WP_Error 是否发送成功
 */
function wp_telegram_send_test_message() {
    $bot_token = get_option('wp_telegram_integration_bot_token', '');
    $channel_id = get_option('wp_telegram_integration_channel_id', '');
    
    if (empty($bot_token) || empty($channel_id)) {
        return new WP_Error('missing_config', '请先配置Bot Token和频道ID');
    }
    
    $message = "🔔 *测试消息*\n\n这是一条来自WordPress的测试消息。\n\n如果您看到这条消息，说明Telegram集成插件配置已成功！\n\n发送时间: " . date('Y-m-d H:i:s');
    
    return wp_telegram_send_message($bot_token, $channel_id, $message);
}

/**
 * 测试Telegram API连接
 * 
 * @return string|WP_Error 连接结果
 */
function wp_telegram_test_connection() {
    $bot_token = get_option('wp_telegram_integration_bot_token', '');
    
    if (empty($bot_token)) {
        return new WP_Error('missing_config', '请先配置Bot Token');
    }
    
    $url = "https://api.telegram.org/bot{$bot_token}/getMe";
    
    $args = array(
        'timeout' => 30,
        'redirection' => 5,
        'httpversion' => '1.0',
        'blocking' => true,
        'headers' => array(),
        'cookies' => array()
    );
    
    // 应用代理设置
    $args = wp_telegram_apply_proxy($args);
    
    wp_telegram_log('测试Telegram API连接', 'debug');
    $response = wp_remote_get($url, $args);
    
    if (is_wp_error($response)) {
        wp_telegram_log('API连接测试失败: ' . $response->get_error_message(), 'error');
        return $response;
    }
    
    $body = json_decode(wp_remote_retrieve_body($response), true);
    $status_code = wp_remote_retrieve_response_code($response);
    
    if ($status_code !== 200 || !isset($body['ok']) || $body['ok'] !== true) {
        $error_message = isset($body['description']) ? $body['description'] : '未知错误 (HTTP ' . $status_code . ')';
        wp_telegram_log('API连接测试错误: ' . $error_message, 'error');
        return new WP_Error('telegram_api_error', $error_message);
    }
    
    $bot_name = isset($body['result']['username']) ? $body['result']['username'] : '未知';
    $bot_first_name = isset($body['result']['first_name']) ? $body['result']['first_name'] : '';
    
    wp_telegram_log('API连接测试成功，Bot用户名: @' . $bot_name, 'info');
    return 'Bot名称: ' . $bot_first_name . ' (@' . $bot_name . ')';
}

/**
 * 应用代理设置到请求参数
 * 
 * @param array $args 请求参数
 * @return array 应用代理后的请求参数
 */
function wp_telegram_apply_proxy($args) {
    $proxy = get_option('wp_telegram_integration_proxy', '');
    
    if (!empty($proxy)) {
        $args['proxy'] = $proxy;
        wp_telegram_log('使用代理: ' . $proxy, 'debug');
    }
    
    return $args;
}

/**
 * 注册发布文章时的钩子
 */
function wp_telegram_register_post_hook() {
    // 移除原有插件的钩子
    if (has_action('wp_insert_post', 'wp_telegram_sync_publish_post')) {
        remove_action('wp_insert_post', 'wp_telegram_sync_publish_post');
    }
    if (has_action('wp_insert_post', 'wp_telegram_sync_publish_post_fixed')) {
        remove_action('wp_insert_post', 'wp_telegram_sync_publish_post_fixed');
    }
    if (has_action('wp_insert_post', 'wp_telegram_sync_publish_post_image_fixed')) {
        remove_action('wp_insert_post', 'wp_telegram_sync_publish_post_image_fixed');
    }
    if (has_action('wp_insert_post', 'wp_telegram_sync_publish_post_complete_fixed')) {
        remove_action('wp_insert_post', 'wp_telegram_sync_publish_post_complete_fixed');
    }
    
    // 添加新的钩子
    add_action('wp_insert_post', 'wp_telegram_publish_post', 10, 3);
}
add_action('init', 'wp_telegram_register_post_hook'); 