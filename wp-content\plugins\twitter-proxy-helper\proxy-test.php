<?php
/**
 * Twitter API Proxy 测试脚本
 * 
 * 这个脚本用于测试Twitter API的代理连接
 */

// 加载WordPress环境 - 使用更可靠的方法查找WordPress根目录
function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        // 向上一级目录寻找wp-load.php
        if (file_exists($dir . '/wp-load.php')) {
            return $dir;
        }
    } while ($dir = realpath("$dir/.."));
    
    // 如果找不到，尝试另一种方法
    $dir = $_SERVER['DOCUMENT_ROOT'];
    if (file_exists($dir . '/wp-load.php')) {
        return $dir;
    }
    
    // 如果还是找不到，尝试常见的相对路径
    $possible_paths = array(
        dirname(dirname(dirname(dirname(__FILE__)))),
        dirname(dirname(dirname(__FILE__))),
        $_SERVER['DOCUMENT_ROOT'],
        '/var/www/html',
        '/mnt/sdc'
    );
    
    foreach ($possible_paths as $path) {
        if (file_exists($path . '/wp-load.php')) {
            return $path;
        }
    }
    
    // 输出路径信息用于调试
    echo "调试信息 - 当前路径: " . __FILE__ . "<br>";
    echo "尝试以下路径:<br>";
    foreach ($possible_paths as $path) {
        echo "$path - " . (file_exists($path . '/wp-load.php') ? "存在" : "不存在") . "<br>";
    }
    
    die('无法找到WordPress安装路径。请手动设置wp-load.php的路径。');
}

define('WP_USE_THEMES', false);
$wp_load_path = find_wordpress_base_path() . '/wp-load.php';

// 输出找到的路径用于调试
echo "<!-- 使用WordPress路径: $wp_load_path -->\n";

// 引入WordPress
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('无法找到WordPress文件 (wp-load.php)。请手动设置正确的路径。');
}

// 安全检查
if (!current_user_can('manage_options')) {
    die('权限不足');
}

// 设置页面头
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Twitter API 代理测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .section { margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <h1>Twitter API 代理测试工具</h1>
    <p>此工具用于测试WordPress连接Twitter API的代理设置</p>
    
    <div class="section">
        <h2>1. 代理服务器基本信息</h2>
        <?php
        echo '<p>代理设置: <strong>socks5://13.209.96.45:7890</strong> (韩国AWS服务器)</p>';
        
        // 测试代理服务器连接
        echo '<h3>代理服务器连接测试</h3>';
        $ch = curl_init('https://api.twitter.com/');
        curl_setopt($ch, CURLOPT_PROXY, '13.209.96.45');
        curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
        curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // SOCKS5
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        
        $start = microtime(true);
        $result = curl_exec($ch);
        $time = round((microtime(true) - $start) * 1000);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($error) {
            echo '<p class="error">代理连接失败: ' . $error . '</p>';
        } else {
            echo '<p class="success">代理连接成功! HTTP状态码: ' . $httpCode . ' (响应时间: ' . $time . 'ms)</p>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>2. 测试Twitter API连接</h2>
        <?php
        // 测试Twitter API连接
        $test_url = 'https://api.twitter.com/2/openapi.json';
        echo '<p>测试URL: ' . $test_url . '</p>';
        
        // 不使用代理
        echo '<h3>2.1 直接连接(不使用代理)</h3>';
        $ch = curl_init($test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        
        $start = microtime(true);
        $result = curl_exec($ch);
        $time = round((microtime(true) - $start) * 1000);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo '<p>HTTP状态码: ' . $httpCode . ' (响应时间: ' . $time . 'ms)</p>';
        if ($error) {
            echo '<p class="error">错误: ' . $error . '</p>';
        } else {
            echo '<pre>' . htmlspecialchars(substr($result, 0, 1000)) . (strlen($result) > 1000 ? '...' : '') . '</pre>';
        }
        
        // 使用韩国AWS代理
        echo '<h3>2.2 使用韩国AWS代理</h3>';
        $ch = curl_init($test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($ch, CURLOPT_PROXY, '13.209.96.45');
        curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
        curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // SOCKS5
        
        $start = microtime(true);
        $result = curl_exec($ch);
        $time = round((microtime(true) - $start) * 1000);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo '<p>HTTP状态码: ' . $httpCode . ' (响应时间: ' . $time . 'ms)</p>';
        if ($error) {
            echo '<p class="error">错误: ' . $error . '</p>';
        } else {
            echo '<pre>' . htmlspecialchars(substr($result, 0, 1000)) . (strlen($result) > 1000 ? '...' : '') . '</pre>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>3. Twitter OAuth 设置检查</h2>
        <?php
        $client_id = get_option('xyz_twap_client_id');
        $client_secret = get_option('xyz_twap_client_secret');
        $token = get_option('xyz_twap_tw_token');
        $refresh_token = get_option('xyz_twap_tw_refresh_token');
        $last_auth = get_option('xyz_twap_last_auth_time');
        
        echo '<ul>';
        echo '<li>Client ID: ' . (empty($client_id) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span> (' . substr($client_id, 0, 5) . '...)') . '</li>';
        echo '<li>Client Secret: ' . (empty($client_secret) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span> (' . substr($client_secret, 0, 5) . '...)') . '</li>';
        echo '<li>Access Token: ' . (empty($token) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span> (' . substr($token, 0, 5) . '...)') . '</li>';
        echo '<li>Refresh Token: ' . (empty($refresh_token) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span> (' . substr($refresh_token, 0, 5) . '...)') . '</li>';
        echo '<li>上次授权时间: ' . (empty($last_auth) ? '<span class="error">未设置</span>' : '<span class="success">' . date('Y-m-d H:i:s', $last_auth) . '</span>') . '</li>';
        echo '</ul>';
        
        if (!empty($token) && !empty($refresh_token)) {
            echo '<p class="success">OAuth设置有效，可以尝试使用下面的发布测试按钮进行测试</p>';
        } else {
            echo '<p class="warning">OAuth设置不完整，请先配置Twitter插件</p>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>4. 发布测试</h2>
        <?php
        if (isset($_POST['twitter_test_post']) && check_admin_referer('twitter_test_post_nonce')) {
            $test_message = !empty($_POST['test_message']) ? 
                $_POST['test_message'] : 
                '这是一条测试推文，由WordPress自动发布 ' . date('Y-m-d H:i:s');
            
            // 使用Twitter插件的函数发布测试推文
            if (function_exists('xyz_twap_publish_to_twitter')) {
                try {
                    echo '<h3>尝试发布测试推文</h3>';
                    echo '<p>推文内容: ' . htmlspecialchars($test_message) . '</p>';
                    
                    // 记录开始时间
                    $start_time = microtime(true);
                    
                    // 调用发布函数
                    $result = xyz_twap_publish_to_twitter(
                        $test_message, // 消息内容
                        '', // 图片URL
                        0   // 假文章ID
                    );
                    
                    $time_taken = round((microtime(true) - $start_time) * 1000);
                    
                    if ($result === true) {
                        echo '<p class="success">发布成功! (耗时: ' . $time_taken . 'ms)</p>';
                    } else {
                        echo '<p class="error">发布失败: ' . $result . ' (耗时: ' . $time_taken . 'ms)</p>';
                    }
                } catch (Exception $e) {
                    echo '<p class="error">发布时发生异常: ' . $e->getMessage() . '</p>';
                }
            } else {
                echo '<p class="error">Twitter发布函数不可用，请确认插件正常激活</p>';
            }
        }
        ?>
        <form method="post">
            <?php wp_nonce_field('twitter_test_post_nonce'); ?>
            <p><label for="test_message">测试推文内容:</label></p>
            <p><textarea name="test_message" id="test_message" rows="3" style="width: 100%;"><?php echo '这是一条测试推文，由WordPress自动发布 ' . date('Y-m-d H:i:s'); ?></textarea></p>
            <p><button type="submit" name="twitter_test_post" class="button button-primary">发布测试推文</button></p>
            <p><small>注意: 该测试将实际发布到您的Twitter账号，请谨慎使用</small></p>
        </form>
    </div>
    
    <div class="section">
        <h2>5. 系统信息</h2>
        <ul>
            <li>PHP版本: <?php echo phpversion(); ?></li>
            <li>WordPress版本: <?php echo get_bloginfo('version'); ?></li>
            <li>cURL版本: <?php 
                if (function_exists('curl_version')) {
                    $curl_info = curl_version();
                    echo $curl_info['version'] . ' (SSL版本: ' . $curl_info['ssl_version'] . ')';
                } else {
                    echo '<span class="error">不可用</span>';
                }
            ?></li>
            <li>WordPress代理设置:
                <ul>
                    <li>WP_PROXY_HOST: <?php echo defined('WP_PROXY_HOST') ? WP_PROXY_HOST : '未设置'; ?></li>
                    <li>WP_PROXY_PORT: <?php echo defined('WP_PROXY_PORT') ? WP_PROXY_PORT : '未设置'; ?></li>
                    <li>WP_PROXY_SOCKS5: <?php echo defined('WP_PROXY_SOCKS5') ? (WP_PROXY_SOCKS5 ? '是' : '否') : '未设置'; ?></li>
                </ul>
            </li>
            <li>环境变量:
                <ul>
                    <li>http_proxy: <?php echo getenv('http_proxy') ?: '未设置'; ?></li>
                    <li>https_proxy: <?php echo getenv('https_proxy') ?: '未设置'; ?></li>
                </ul>
            </li>
        </ul>
    </div>
    
    <div class="section">
        <h2>6. 日志查看</h2>
        <?php
        $log_file = WP_CONTENT_DIR . '/twitter-proxy-debug.log';
        if (file_exists($log_file)) {
            $log_size = filesize($log_file);
            $human_size = round($log_size / 1024, 2) . ' KB';
            echo '<p>代理日志文件: ' . $log_file . ' (大小: ' . $human_size . ')</p>';
            
            // 读取最后20行日志
            $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            if ($lines) {
                $last_lines = array_slice($lines, -20);
                echo '<h3>最近的日志记录 (最后20行)</h3>';
                echo '<pre>' . implode("\n", $last_lines) . '</pre>';
            }
        } else {
            echo '<p class="warning">日志文件不存在</p>';
        }
        ?>
        
        <?php if (file_exists($log_file)): ?>
        <form method="post">
            <?php wp_nonce_field('twitter_clear_log_nonce'); ?>
            <p><button type="submit" name="twitter_clear_log" class="button button-secondary">清空日志</button></p>
        </form>
        <?php 
        if (isset($_POST['twitter_clear_log']) && check_admin_referer('twitter_clear_log_nonce')) {
            // 清空日志文件
            file_put_contents($log_file, '');
            echo '<p class="success">日志已清空</p>';
        }
        ?>
        <?php endif; ?>
    </div>
    
    <p><a href="<?php echo admin_url('admin.php?page=twitter-auto-publish-settings'); ?>" class="button">返回Twitter插件设置</a></p>
</body>
</html> 