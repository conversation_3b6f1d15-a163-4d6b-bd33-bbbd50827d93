<?php
// 如果直接访问插件文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

// 添加设置菜单
function wp_to_sina_add_menu() {
    add_options_page(
        'WP到新浪微博设置', 
        'WP到新浪微博', 
        'manage_options', 
        'wp-to-sina', 
        'wp_to_sina_settings_page'
    );
}
add_action('admin_menu', 'wp_to_sina_add_menu');

// 注册设置
function wp_to_sina_register_settings() {
    register_setting('wp_to_sina_settings', 'wp_to_sina_app_key');
    register_setting('wp_to_sina_settings', 'wp_to_sina_app_secret');
    register_setting('wp_to_sina_settings', 'wp_to_sina_access_token');
    register_setting('wp_to_sina_settings', 'wp_to_sina_auto_sync');
    register_setting('wp_to_sina_settings', 'wp_to_sina_content_format');
}
add_action('admin_init', 'wp_to_sina_register_settings');

// 设置页面
function wp_to_sina_settings_page() {
    $access_token = get_option('wp_to_sina_access_token');
    $app_key = get_option('wp_to_sina_app_key');
    $redirect_uri = admin_url('options-general.php?page=wp-to-sina');
    
    // 处理OAuth回调
    if (isset($_GET['code']) && !empty($_GET['code'])) {
        wp_to_sina_handle_oauth($_GET['code']);
    }
    
    // 处理取消授权
    if (isset($_POST['wp_to_sina_revoke']) && $_POST['wp_to_sina_revoke'] == '1') {
        delete_option('wp_to_sina_access_token');
        echo '<div class="notice notice-success is-dismissible"><p>已取消微博授权</p></div>';
    }
    
    ?>
    <div class="wrap">
        <h1>WordPress 到新浪微博同步设置</h1>
        
        <form method="post" action="options.php">
            <?php settings_fields('wp_to_sina_settings'); ?>
            
            <table class="form-table">
                <tr>
                    <th>微博开放平台App Key</th>
                    <td>
                        <input type="text" name="wp_to_sina_app_key" value="<?php echo esc_attr(get_option('wp_to_sina_app_key')); ?>" class="regular-text" />
                        <p class="description">在<a href="https://open.weibo.com/" target="_blank">微博开放平台</a>创建应用后获取的App Key</p>
                    </td>
                </tr>
                
                <tr>
                    <th>微博开放平台App Secret</th>
                    <td>
                        <input type="password" name="wp_to_sina_app_secret" value="<?php echo esc_attr(get_option('wp_to_sina_app_secret')); ?>" class="regular-text" />
                        <p class="description">在微博开放平台创建应用后获取的App Secret</p>
                    </td>
                </tr>
                
                <tr>
                    <th>微博授权</th>
                    <td>
                        <?php if ($access_token): ?>
                            <p><strong>已授权</strong></p>
                            <a href="#" class="button" onclick="if(confirm('确定要取消授权吗？')) { document.getElementById('wp_to_sina_revoke').value='1'; this.form.submit(); } return false;">取消授权</a>
                            <input type="hidden" name="wp_to_sina_revoke" id="wp_to_sina_revoke" value="0" />
                        <?php else: ?>
                            <?php if ($app_key): ?>
                                <a href="https://api.weibo.com/oauth2/authorize?client_id=<?php echo esc_attr($app_key); ?>&response_type=code&redirect_uri=<?php echo esc_url($redirect_uri); ?>" class="button button-primary">授权微博账号</a>
                            <?php else: ?>
                                <p>请先填写App Key和App Secret，然后保存设置后再进行授权。</p>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                </tr>
                
                <tr>
                    <th>自动同步</th>
                    <td>
                        <label>
                            <input type="checkbox" name="wp_to_sina_auto_sync" value="1" <?php checked(get_option('wp_to_sina_auto_sync'), 1); ?> />
                            启用文章发布时自动同步到微博
                        </label>
                    </td>
                </tr>
                
                <tr>
                    <th>微博内容格式</th>
                    <td>
                        <textarea name="wp_to_sina_content_format" rows="4" class="large-text"><?php echo esc_textarea(get_option('wp_to_sina_content_format', '【{title}】{excerpt} {url}')); ?></textarea>
                        <p class="description">
                            可用的变量：<br />
                            {title} - 文章标题<br />
                            {excerpt} - 文章摘要<br />
                            {url} - 文章链接
                        </p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(); ?>
        </form>
        
        <hr />
        
        <h2>使用说明</h2>
        <ol>
            <li>在<a href="https://open.weibo.com/" target="_blank">微博开放平台</a>创建应用</li>
            <li>填写您的App Key和App Secret</li>
            <li>点击"授权微博账号"按钮进行授权</li>
            <li>发布新文章时，将自动同步到微博</li>
        </ol>
    </div>
    <?php
}

// 处理OAuth回调
function wp_to_sina_handle_oauth($code) {
    $app_key = get_option('wp_to_sina_app_key');
    $app_secret = get_option('wp_to_sina_app_secret');
    $redirect_uri = admin_url('options-general.php?page=wp-to-sina');
    
    // 获取访问令牌
    $response = wp_remote_post('https://api.weibo.com/oauth2/access_token', array(
        'body' => array(
            'client_id' => $app_key,
            'client_secret' => $app_secret,
            'grant_type' => 'authorization_code',
            'code' => $code,
            'redirect_uri' => $redirect_uri
        )
    ));
    
    if (!is_wp_error($response)) {
        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (isset($body['access_token'])) {
            update_option('wp_to_sina_access_token', $body['access_token']);
            echo '<div class="notice notice-success is-dismissible"><p>微博授权成功！您现在可以同步文章到微博了。</p></div>';
        } else {
            echo '<div class="notice notice-error is-dismissible"><p>微博授权失败：' . (isset($body['error_description']) ? esc_html($body['error_description']) : '未知错误') . '</p></div>';
        }
    } else {
        echo '<div class="notice notice-error is-dismissible"><p>微博授权失败：' . esc_html($response->get_error_message()) . '</p></div>';
    }
}

// 添加文章元框
function wp_to_sina_add_meta_box() {
    add_meta_box(
        'wp_to_sina_meta_box',
        '新浪微博同步设置',
        'wp_to_sina_meta_box_content',
        'post',
        'side',
        'default'
    );
}
add_action('add_meta_boxes', 'wp_to_sina_add_meta_box');

// 显示元框内容
function wp_to_sina_meta_box_content($post) {
    // 添加nonce验证
    wp_nonce_field('wp_to_sina_meta_box', 'wp_to_sina_meta_box_nonce');
    
    // 获取当前设置
    $sync_to_sina = get_post_meta($post->ID, '_sync_to_sina', true);
    $sina_status = get_post_meta($post->ID, '_sina_status', true);
    
    // 如果是新文章，默认根据全局设置
    if ($sync_to_sina === '') {
        $sync_to_sina = '1';
    }
    
    ?>
    <p>
        <label>
            <input type="checkbox" name="sync_to_sina" value="1" <?php checked($sync_to_sina, '1'); ?> />
            同步到新浪微博
        </label>
    </p>
    
    <?php if ($sina_status) : ?>
        <p>
            <strong>同步状态:</strong><br />
            <?php echo esc_html($sina_status); ?>
        </p>
    <?php endif; ?>
    
    <?php if ($post->post_status == 'publish' && !$sina_status) : ?>
        <p>
            <button type="button" class="button" onclick="document.getElementById('manual_sync_to_sina').value='1'; return true;">立即同步到微博</button>
            <input type="hidden" name="manual_sync_to_sina" id="manual_sync_to_sina" value="0" />
        </p>
    <?php endif; ?>
    <?php
}

// 保存元数据
function wp_to_sina_save_meta($post_id) {
    // 安全检查
    if (!isset($_POST['wp_to_sina_meta_box_nonce']) || !wp_verify_nonce($_POST['wp_to_sina_meta_box_nonce'], 'wp_to_sina_meta_box')) {
        return;
    }
    
    // 如果是自动保存，不处理
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    // 检查权限
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // 保存同步设置
    $sync_to_sina = isset($_POST['sync_to_sina']) ? '1' : '0';
    update_post_meta($post_id, '_sync_to_sina', $sync_to_sina);
    
    // 处理手动同步请求
    if (isset($_POST['manual_sync_to_sina']) && $_POST['manual_sync_to_sina'] == '1') {
        wp_to_sina_do_sync($post_id);
    }
}
add_action('save_post', 'wp_to_sina_save_meta');

// 处理文章发布时的微博同步
function wp_to_sina_publish_post($post_id, $post) {
    // 只处理文章类型
    if ($post->post_type != 'post') {
        return;
    }
    
    // 检查是否应该同步
    if (!wp_to_sina_should_sync($post_id)) {
        return;
    }
    
    // 执行同步
    wp_to_sina_do_sync($post_id);
}
add_action('publish_post', 'wp_to_sina_publish_post', 10, 2);

// 检查是否应该同步文章
function wp_to_sina_should_sync($post_id) {
    // 检查文章同步设置
    $sync_to_sina = get_post_meta($post_id, '_sync_to_sina', true);
    
    // 如果明确设置不同步，则不同步
    if ($sync_to_sina === '0') {
        return false;
    }
    
    // 检查全局自动同步设置
    $auto_sync = get_option('wp_to_sina_auto_sync');
    if (!$auto_sync) {
        return false;
    }
    
    return true;
}

// 执行微博同步
function wp_to_sina_do_sync($post_id) {
    // 获取访问令牌
    $access_token = get_option('wp_to_sina_access_token');
    if (!$access_token) {
        update_post_meta($post_id, '_sina_status', '同步失败：未授权微博账号');
        return false;
    }
    
    // 获取文章信息
    $post = get_post($post_id);
    if (!$post) {
        return false;
    }
    
    // 准备微博内容
    $sina_content = wp_to_sina_prepare_content($post);
    
    // 发送到微博
    $result = wp_to_sina_post_status($access_token, $sina_content);
    
    // 处理结果
    if (isset($result['id'])) {
        // 成功发布
        update_post_meta($post_id, '_sina_status', '同步成功：' . date('Y-m-d H:i:s'));
        return true;
    } else {
        // 发布失败
        $error_message = isset($result['error']) ? $result['error'] : '未知错误';
        update_post_meta($post_id, '_sina_status', '同步失败：' . $error_message);
        return false;
    }
}

// 准备微博内容
function wp_to_sina_prepare_content($post) {
    // 获取内容格式
    $format = get_option('wp_to_sina_content_format', '【{title}】{excerpt} {url}');
    
    // 获取文章摘要
    $excerpt = '';
    if (!empty($post->post_excerpt)) {
        $excerpt = $post->post_excerpt;
    } else {
        $excerpt = wp_strip_all_tags($post->post_content);
        $excerpt = wp_trim_words($excerpt, 30, '...');
    }
    
    // 替换变量
    $content = str_replace(
        array('{title}', '{excerpt}', '{url}'),
        array(
            $post->post_title,
            $excerpt,
            get_permalink($post->ID)
        ),
        $format
    );
    
    // 确保不超过微博字数限制（140个字符）
    if (mb_strlen($content, 'UTF-8') > 140) {
        $content = mb_substr($content, 0, 137, 'UTF-8') . '...';
    }
    
    return $content;
}

// 发送纯文本微博
function wp_to_sina_post_status($access_token, $content) {
    $response = wp_remote_post('https://api.weibo.com/2/statuses/update.json', array(
        'method' => 'POST',
        'body' => array(
            'access_token' => $access_token,
            'status' => $content
        )
    ));
    
    if (is_wp_error($response)) {
        return array('error' => $response->get_error_message());
    }
    
    return json_decode(wp_remote_retrieve_body($response), true);
} 