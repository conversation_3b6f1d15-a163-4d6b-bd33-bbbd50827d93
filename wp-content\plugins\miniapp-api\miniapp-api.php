<?php
/*
Plugin Name: MiniApp API
Plugin URI: https://your-plugin-url.com
Description: WordPress MiniApp API Plugin for WeChat Mini Program
Version: 1.0.0
Author: Your Name
Author URI: https://your-website.com
License: GPLv2 or later
Text Domain: miniapp-api
*/

if (!defined('ABSPATH')) {
    exit;
}

// 获取下载信息
function get_article_download_info($post_id) {
    $post = get_post($post_id);
    if (!$post) {
        return [
            'code' => -1,
            'message' => '文章不存在'
        ];
    }

    // 获取下载信息
    $pay_mate = get_post_meta($post_id, 'posts_zibpay', true);
    if (!$pay_mate) {
        return [
            'code' => -1,
            'message' => '未找到下载信息'
        ];
    }

    // 处理下载信息
    $down_info = array();
    if (is_array($pay_mate['pay_download'])) {
        $down_info = $pay_mate['pay_download'];
    } else {
        $down = explode("\r\n", $pay_mate['pay_download']);
        foreach ($down as $down_v) {
            $down_v = explode("|", $down_v);
            if (empty($down_v[0])) continue;
            
            $down_info[] = array(
                'link' => trim($down_v[0]),
                'name' => !empty($down_v[1]) ? trim($down_v[1]) : '',
                'more' => !empty($down_v[2]) ? trim($down_v[2]) : '',
                'class' => !empty($down_v[3]) ? trim($down_v[3]) : ''
            );
        }
    }

    // 获取第一个下载链接作为主下载链接
    $first_down = !empty($down_info[0]) ? $down_info[0] : array();
    $download_url = !empty($first_down['link']) ? $first_down['link'] : '';
    // 使用 copy_val 作为提取码
    $extract_code = !empty($first_down['copy_val']) ? $first_down['copy_val'] : '';

    // 更新下载次数
    $download_count = intval(get_post_meta($post_id, 'download_count', true));
    update_post_meta($post_id, 'download_count', $download_count + 1);

    // 返回所有信息
    return [
        'code' => 0,
        'message' => 'success',
        'data' => [
            'content' => $post->post_content,
            'downloadUrl' => $download_url,
            'extractCode' => $extract_code,
            'downloads' => $download_count + 1,
            'meta' => $pay_mate,
            'downloadInfo' => $down_info
        ]
    ];
}

// 获取文章列表
function get_articles_list($page = 1, $per_page = 10, $category = null) {
    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC'
    );

    // 添加分类过滤
    if ($category && is_numeric($category)) {
        $args['cat'] = intval($category);
    }

    $query = new WP_Query($args);
    $posts = array();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            // 获取付费下载信息
            $pay_mate = get_post_meta(get_the_ID(), 'posts_zibpay', true);
            
            // 处理价格逻辑
            $price = 0;  // 默认免费
            $is_paid = !empty($pay_mate) && isset($pay_mate['pay_type']) && $pay_mate['pay_type'] !== 'no';  // 是否付费文章
            
            if ($is_paid) {
                // 如果有自定义价格，使用自定义价格
                if (!empty($pay_mate['price'])) {
                    $price = floatval($pay_mate['price']);
                } else {
                    $price = 9.9;  // 默认付费价格
                }
            }
            
            $post = array(
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'excerpt' => get_the_excerpt(),
                'date' => get_the_date('Y-m-d H:i:s'),
                'thumbnail' => get_the_post_thumbnail_url(null, 'full'),
                'categories' => wp_get_post_categories(get_the_ID(), array('fields' => 'all')),
                'author' => get_the_author(),
                'link' => get_permalink(),
                'isPaid' => $is_paid,
                'isVip' => true,
                'price' => $price,
                'downloads' => intval(get_post_meta(get_the_ID(), 'download_count', true))
            );
            $posts[] = $post;
        }
        wp_reset_postdata();
    }

    return array(
        'code' => 0,
        'message' => 'success',
        'data' => array(
            'list' => $posts,
            'pagination' => array(
                'total' => $query->found_posts,
                'totalPages' => $query->max_num_pages,
                'page' => intval($page),
                'pageSize' => intval($per_page),
                'hasMore' => intval($page) < $query->max_num_pages
            )
        )
    );
}

// 获取文章详情
function get_article_detail($post_id) {
    $post = get_post($post_id);
    if (!$post) {
        return [
            'code' => -1,
            'message' => '文章不存在'
        ];
    }

    // 获取付费下载信息
    $pay_mate = get_post_meta($post_id, 'posts_zibpay', true);
    
    // 处理价格逻辑
    $price = 0;  // 默认免费
    $is_paid = !empty($pay_mate) && isset($pay_mate['pay_type']) && $pay_mate['pay_type'] !== 'no';  // 是否付费文章
    
    if ($is_paid) {
        // 如果有自定义价格，使用自定义价格
        if (!empty($pay_mate['price'])) {
            $price = floatval($pay_mate['price']);
        } else {
            $price = 9.9;  // 默认付费价格
        }
    }
    
    $article = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'content' => $post->post_content,
        'excerpt' => get_the_excerpt($post),
        'date' => get_the_date('Y-m-d H:i:s', $post),
        'modified' => get_the_modified_date('Y-m-d H:i:s', $post),
        'thumbnail' => get_the_post_thumbnail_url($post, 'full'),
        'categories' => wp_get_post_categories($post->ID, array('fields' => 'all')),
        'author' => get_the_author_meta('display_name', $post->post_author),
        'link' => get_permalink($post),
        'isPaid' => $is_paid,
        'isVip' => true,
        'price' => $price,
        'downloads' => intval(get_post_meta($post->ID, 'download_count', true))
    );

    return array(
        'code' => 0,
        'message' => 'success',
        'data' => $article
    );
}

// 获取分类列表
function get_categories_list() {
    $categories = get_categories(array(
        'orderby' => 'count',
        'order' => 'DESC',
        'hide_empty' => false
    ));

    $formatted_categories = array_map(function($category) {
        return array(
            'id' => $category->term_id,
            'name' => $category->name,
            'slug' => $category->slug,
            'count' => $category->count,
            'description' => $category->description
        );
    }, $categories);

    return array(
        'code' => 0,
        'message' => 'success',
        'data' => $formatted_categories
    );
}

// 注册 REST API 端点
add_action('rest_api_init', function () {
    // 获取文章列表
    register_rest_route('miniapp/v1', '/posts', array(
        'methods' => 'GET',
        'callback' => function ($request) {
            $page = $request->get_param('page') ?: 1;
            $per_page = $request->get_param('per_page') ?: 10;
            $category = $request->get_param('category');
            return get_articles_list($page, $per_page, $category);
        },
        'permission_callback' => function () {
            return true;
        }
    ));

    // 获取文章下载信息
    register_rest_route('miniapp/v1', '/post-download/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => function ($request) {
            $post_id = $request['id'];
            return get_article_download_info($post_id);
        },
        'permission_callback' => function () {
            return true;
        }
    ));

    // 获取文章详情
    register_rest_route('miniapp/v1', '/posts/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => function ($request) {
            $post_id = $request['id'];
            return get_article_detail($post_id);
        },
        'permission_callback' => function () {
            return true;
        }
    ));

    // 获取分类列表
    register_rest_route('miniapp/v1', '/categories', array(
        'methods' => 'GET',
        'callback' => function ($request) {
            return get_categories_list();
        },
        'permission_callback' => function () {
            return true;
        }
    ));
}); 