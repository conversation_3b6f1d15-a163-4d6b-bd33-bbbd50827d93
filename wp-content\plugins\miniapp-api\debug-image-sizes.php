<?php
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';

// 确保只有管理员可以访问
if (!current_user_can('manage_options')) {
    die('需要管理员权限');
}

echo '<h2>WordPress 图片尺寸设置</h2>';

// 显示默认的媒体设置
echo '<h3>默认媒体设置</h3>';
echo '<ul>';
echo '<li>缩略图尺寸: ' . get_option('thumbnail_size_w') . 'x' . get_option('thumbnail_size_h') . ' - 裁剪模式: ' . (get_option('thumbnail_crop') ? '是' : '否') . '</li>';
echo '<li>中等尺寸: ' . get_option('medium_size_w') . 'x' . get_option('medium_size_h') . '</li>';
echo '<li>大尺寸: ' . get_option('large_size_w') . 'x' . get_option('large_size_h') . '</li>';
echo '</ul>';

// 显示所有注册的图片尺寸
echo '<h3>所有注册的图片尺寸</h3>';
global $_wp_additional_image_sizes;
$sizes = array();

// 获取默认尺寸
$sizes['thumbnail'] = array(
    'width' => get_option('thumbnail_size_w'),
    'height' => get_option('thumbnail_size_h'),
    'crop' => get_option('thumbnail_crop')
);
$sizes['medium'] = array(
    'width' => get_option('medium_size_w'),
    'height' => get_option('medium_size_h'),
    'crop' => false
);
$sizes['large'] = array(
    'width' => get_option('large_size_w'),
    'height' => get_option('large_size_h'),
    'crop' => false
);

// 获取其他注册的尺寸
if (is_array($_wp_additional_image_sizes)) {
    $sizes = array_merge($sizes, $_wp_additional_image_sizes);
}

echo '<table border="1" cellpadding="5">';
echo '<tr><th>尺寸名称</th><th>宽度</th><th>高度</th><th>裁剪模式</th></tr>';

foreach ($sizes as $size => $value) {
    echo '<tr>';
    echo '<td>' . esc_html($size) . '</td>';
    echo '<td>' . esc_html($value['width']) . '</td>';
    echo '<td>' . esc_html($value['height']) . '</td>';
    echo '<td>' . (isset($value['crop']) && $value['crop'] ? '是' : '否') . '</td>';
    echo '</tr>';
}

echo '</table>';

// 显示当前主题支持的特色图片尺寸
echo '<h3>主题支持的特色图片</h3>';
if (current_theme_supports('post-thumbnails')) {
    echo '当前主题支持特色图片';
} else {
    echo '当前主题不支持特色图片';
} 