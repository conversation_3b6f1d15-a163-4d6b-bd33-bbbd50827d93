<?php
/**
 * Plugin Name: 文章网盘链接提取器
 * Plugin URI: https://wczk.wzam.cn
 * Description: 自动提取文章中的夸克网盘和百度网盘链接，并显示在文章底部
 * Version: 1.0
 * Author: Claude AI
 * Author URI: https://wczk.wzam.cn
 * License: GPL2
 */

// 如果直接访问插件文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 提取文章中的夸克网盘和百度网盘链接
 * 
 * @param string $content 文章内容
 * @return array 提取到的链接数组
 */
function pdl_extract_disk_links($content) {
    $links = array();
    
    // 夸克网盘链接匹配规则
    $quark_pattern = '/https?:\/\/pan\.quark\.cn\/s\/[a-zA-Z0-9]+/i';
    
    // 百度网盘链接匹配规则
    $baidu_pattern = '/https?:\/\/(?:pan|yun)\.baidu\.com\/s\/[a-zA-Z0-9_-]+/i';
    
    // 提取夸克网盘链接
    if (preg_match_all($quark_pattern, $content, $quark_matches)) {
        foreach ($quark_matches[0] as $match) {
            $links[] = array(
                'url' => $match,
                'type' => 'quark',
                'name' => '夸克网盘'
            );
        }
    }
    
    // 提取百度网盘链接
    if (preg_match_all($baidu_pattern, $content, $baidu_matches)) {
        foreach ($baidu_matches[0] as $match) {
            $links[] = array(
                'url' => $match,
                'type' => 'baidu',
                'name' => '百度网盘'
            );
        }
    }
    
    return $links;
}

/**
 * 提取提取码
 * 
 * @param string $content 文章内容
 * @param string $link_url 网盘链接
 * @return string|bool 提取码或false
 */
function pdl_extract_extraction_code($content, $link_url = '') {
    // 获取文章最后1000个字符
    $content_end = mb_substr($content, -1000);
    
    // 如果提供了链接，尝试找到该链接周围的提取码
    if (!empty($link_url)) {
        // 获取链接周围200个字符
        $pos = strpos($content, $link_url);
        if ($pos !== false) {
            $start_pos = max(0, $pos - 100);
            $surrounding_text = mb_substr($content, $start_pos, 300);
            
            // 匹配常见的提取码格式
            $patterns = array(
                // 链接后面跟着的提取码格式
                '/'.preg_quote($link_url, '/').'.*?(?:提取码|密码|访问码)[：:]\s*([a-zA-Z0-9]{4,})/i',
                // 链接后面的短提取码格式
                '/'.preg_quote($link_url, '/').'.*?[：:\s]+([a-zA-Z0-9]{4})\b/i'
            );
            
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $surrounding_text, $matches)) {
                    return $matches[1];
                }
            }
        }
    }
    
    // 一般提取码匹配模式
    $patterns = array(
        // 匹配"提取码"或"密码"或"访问码"后面跟着的4个或更多字符
        '/(?:提取码|密码|访问码|提\s*取\s*码|验证码|授权码)[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配"密码"后面跟着的4个或更多字符
        '/(?:密\s*码)[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配"提码：xxxx"或"取码:xxxx"格式
        '/(?:提\s*码|取\s*码)[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配格式为"链接：xxx 提取码：xxxx"
        '/链接.*?提取码[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配"网盘密码"格式
        '/网盘密码[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配带emoji的提取码格式
        '/[🔑📌🔐🔒][\s:：]*([a-zA-Z0-9]{4,})/i',
        // 匹配可能的4位提取码格式（只用于最后尝试）
        '/(?<![a-zA-Z0-9])([a-zA-Z0-9]{4})(?![a-zA-Z0-9]).*?(?:网盘|云盘|提取|密码)/i',
    );
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content_end, $matches)) {
            return $matches[1];
        }
    }
    
    return false;
}

/**
 * 格式化网盘链接显示HTML
 * 
 * @param array $links 提取到的链接信息
 * @return string 格式化的HTML
 */
function pdl_format_disk_links_html($links) {
    if (empty($links)) {
        return '';
    }
    
    $html = '';
    
    foreach ($links as $link) {
        // 尝试提取该链接特定的提取码
        $extraction_code = pdl_extract_extraction_code(get_post_field('post_content', get_the_ID()), $link['url']);
        
        // 直接显示链接
        $html .= '<p>';
        $html .= '<a href="' . esc_url($link['url']) . '" target="_blank" rel="nofollow">' . esc_url($link['url']) . '</a>';
        
        // 如果有提取码，直接显示
        if ($extraction_code) {
            $html .= ' 提取码：' . esc_html($extraction_code);
        }
        
        $html .= '</p>';
    }
    
    return $html;
}

/**
 * 将网盘链接添加到文章内容底部
 * 
 * @param string $content 原始文章内容
 * @return string 添加了网盘链接的文章内容
 */
function pdl_append_disk_links($content) {
    // 只在单篇文章或页面显示
    if (!is_singular()) {
        return $content;
    }
    
    // 提取链接
    $links = pdl_extract_disk_links($content);
    
    // 如果没有找到链接，直接返回原内容
    if (empty($links)) {
        return $content;
    }
    
    // 格式化链接HTML
    $links_html = pdl_format_disk_links_html($links);
    
    // 将链接HTML添加到内容底部
    return $content . $links_html;
}

/**
 * 添加样式和脚本
 */
function pdl_enqueue_scripts() {
    // 只在单篇文章或页面加载
    if (!is_singular()) {
        return;
    }
    
    // 注册和加载样式
    wp_enqueue_style('pdl-styles', plugins_url('css/pdl-styles.css', __FILE__), array(), '1.0');
    
    // 注册和加载剪贴板脚本
    wp_enqueue_script('clipboard', 'https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js', array(), '2.0.8', true);
    wp_enqueue_script('pdl-scripts', plugins_url('js/pdl-scripts.js', __FILE__), array('jquery', 'clipboard'), '1.0', true);
}

/**
 * 创建插件所需的文件夹和文件
 */
function pdl_create_plugin_files() {
    // 创建CSS文件夹
    $css_dir = plugin_dir_path(__FILE__) . 'css';
    if (!file_exists($css_dir)) {
        wp_mkdir_p($css_dir);
    }
    
    // 创建JS文件夹
    $js_dir = plugin_dir_path(__FILE__) . 'js';
    if (!file_exists($js_dir)) {
        wp_mkdir_p($js_dir);
    }
    
    // 创建CSS文件
    $css_file = $css_dir . '/pdl-styles.css';
    if (!file_exists($css_file)) {
        $css_content = <<<CSS
.disk-links-container {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
}

.disk-links-title {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 8px;
}

.disk-link-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.disk-link-item svg {
    margin-right: 10px;
    flex-shrink: 0;
}

.disk-link-item a {
    color: #333;
    text-decoration: none;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.disk-link-item a:hover {
    color: #1e88e5;
}

.disk-link-code {
    display: flex;
    align-items: center;
    margin-top: 5px;
    margin-left: 34px;
    padding: 4px 10px;
    background: #fff;
    border-radius: 4px;
    border: 1px dashed #ddd;
}

.extraction-code {
    font-family: monospace;
    font-weight: bold;
    color: #e53935;
    margin: 0 10px;
}

.copy-code-btn {
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 2px 8px;
    cursor: pointer;
    font-size: 12px;
}

.copy-code-btn:hover {
    background: #e0e0e0;
}

.quark-link svg path {
    fill: #F8BD24;
}

.baidu-link svg path {
    fill: #2B5EEE;
}

@media (max-width: 768px) {
    .disk-link-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .disk-link-code {
        margin-left: 0;
        margin-top: 8px;
        width: 100%;
    }
}
CSS;
        file_put_contents($css_file, $css_content);
    }
    
    // 创建JS文件
    $js_file = $js_dir . '/pdl-scripts.js';
    if (!file_exists($js_file)) {
        $js_content = <<<JS
jQuery(document).ready(function($) {
    // 初始化剪贴板功能
    var clipboard = new ClipboardJS('.copy-code-btn');
    
    // 成功复制回调
    clipboard.on('success', function(e) {
        var originalText = $(e.trigger).text();
        $(e.trigger).text('已复制!');
        
        // 2秒后恢复原始文本
        setTimeout(function() {
            $(e.trigger).text(originalText);
        }, 2000);
        
        e.clearSelection();
    });
    
    // 复制失败回调
    clipboard.on('error', function(e) {
        var originalText = $(e.trigger).text();
        $(e.trigger).text('复制失败，请手动复制');
        
        // 2秒后恢复原始文本
        setTimeout(function() {
            $(e.trigger).text(originalText);
        }, 2000);
    });
});
JS;
        file_put_contents($js_file, $js_content);
    }
}

// 在激活插件时创建必要的文件
register_activation_hook(__FILE__, 'pdl_create_plugin_files');

// 添加过滤器，将网盘链接添加到内容
add_filter('the_content', 'pdl_append_disk_links');

// 加载样式和脚本
add_action('wp_enqueue_scripts', 'pdl_enqueue_scripts'); 