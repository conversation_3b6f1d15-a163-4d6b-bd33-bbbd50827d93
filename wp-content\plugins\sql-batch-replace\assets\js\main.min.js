(function(e,a){e(a).ready(function(e){function a(n,o,d,c){if(!o||!d)return t(r.parameter_error,"error");var u={action:"sql_replace_submit",old:o,new:d},v=e(".sql-next").attr("disabled",!0);return l(c),i(n,u,function(i){var l=i.data,u="";if(v.attr("disabled",!1),l&&l.count&&(l.data.routine&&l.data.routine.detail&&(e.each(l.data.routine.detail,function(e,a){~~a.count&&(u+="<div>table["+a.table_name+"] => column["+a.column_name+"] => count["+a.count+"]</div>")}),u&&0==e(".sql-notice").find(".sql-log-title").length&&(u='<div class="sql-log-title">'+r.replace_title+"</div>"+u)),l.data.serialize&&l.data.serialize.detail&&~~l.data.serialize.count&&(0==e(".sql-notice").find(".sql-log-serialize-title").length&&(u+='<div class="sql-log-serialize-title">'+r.replace_serialize_title+"</div>"),e.each(l.data.serialize.detail,function(e,a){~~a.count&&(u+="<div>table["+a.table_name+"] => column["+a.column_name+"] => count["+a.count+"]</div>")}))),u){var m="",p="",_="";if(!l.time_over){var q=r.replace_success_text.replace("{old}",'<span class="'+s+' old">'+l.old+"</span>").replace("{new}",'<span class="'+s+' new">'+l.new+"</span>");m='<div class="sql-success"><div class="sql-notice-title"><span class="dashicons dashicons-yes-alt"></span> '+r.replace_success_title+"</div>"+q+"</div>"}l.msg&&(p='<div class="sql-warning">'+l.msg+"</div>"),_=c?u+m+p:m+p+u,t(_,"info",c),l.time_over?a(n,o,d,!0):n.hide()}else c||t(r.not_reminder,"error")}),!1}function i(a,i,l){if(a.attr("disabled"))return!1;var n=a.html();i.nonce=_sql.nonce,a.attr("disabled",!0).html('<i class="sql-loading"></i>请稍候'),e.ajax({type:"POST",url:_sql.ajax_url,data:i,dataType:"json",error:function(i){return console.error("ajax_error",i),a.attr("disabled",!1).html(n),e(".sql-submit").hide(),t('<div class="sql-notice-title"><span class="dashicons dashicons-dismiss"></span> Ajax Error</div><div>Error Status:'+i.status+"</div><div>Error Msg:"+(i.responseText||i.statusText)+"</div>","error")},success:function(s){if(a.attr("disabled",!1).html(n),!s.success)return e(".sql-submit").hide(),t(s.data,"error");e.isFunction(l)&&l(s,a,i),s.reload&&window.location.reload()}})}function t(a,i,t){var l=e(".sql-notice");i=i||"info",l.removeClass("error success info").addClass(i),t?l.append(a):l.html(a)}function l(a){e(".sql-notice").find(".sql-loading-box").remove(),t('<div class="sql-loading-box"><i class="sql-loading"></i> '+r.loading_text+"</div>","info",a)}var n=e(".sql-replace-wrap"),s="sql-key",r=_sql.lang;n.on("click",".sql-next",function(){var a=e(this),l=e(".sql-input-old").val(),n=e(".sql-input-new").val(),o=e(".sql-submit"),d={action:"sql_replace_next",old:l,new:n};return i(a,d,function(i){var l="",n=0;if(i.data&&i.data.data&&e.each(i.data.data,function(e,a){var i=~~a.count;i&&(n+=i,l+="<div>[table:"+a.table_name+"] => [column:"+a.column_name+"] => count["+a.count+"]"+(~~a.serialize_count?" || serialize_count["+a.serialize_count+"]":"")+"</div>")}),l){var d=r.replace_reminder.replace("{old}",'<span class="'+s+' old">'+i.data.old+"</span>").replace("{new}",'<span class="'+s+' new">'+i.data.new+"</span>"),c=r.replace_count_reminder.replace("{count}",n),u='<div class="sql-warning">'+d+"<div>"+c+"</div></div>";i.data.msg&&(u+='<div class="sql-warning">'+i.data.msg+"</div>"),u+="<div><div>"+r.query_title+"</div>"+l+"</div>",t(u),a.html(r.requery_btn),o.show()}else t(r.not_reminder,"error"),o.hide()}),!1}),n.on("click",".sql-submit",function(){var i=e(this),t=e("."+s+".old").text(),l=e("."+s+".new").text();return!!confirm(r.reconfirm_reminder.replace("{old}",t).replace("{new}",l))&&a(i,t,l)}),console.log("数据库批处理工具：by:zibll-老唐")})})(jQuery,document);