<?php
if( !defined('ABSPATH') ){ exit();}
add_action('admin_menu', 'xyz_twap_menu');

function xyz_twap_add_admin_scripts()
{
	wp_enqueue_script('jquery');
	wp_register_script( 'xyz_notice_script_twap', plugins_url('twitter-auto-publish/js/notice.js') );
	wp_enqueue_script( 'xyz_notice_script_twap' );
	$twap_smapsolution_var="SMAPSolutions";
	$twap_xyzscripts_var="xyzscripts";
	wp_localize_script('xyz_notice_script_twap','xyz_script_twap_var',array(
	    'alert1' => __('Please check whether the email is correct.','twitter-auto-publish'),
	    'alert2' => __('Select atleast one list.','twitter-auto-publish'),
	    'alert3' => __('You do not have sufficient permissions','twitter-auto-publish'),
	    'html1' => sprintf(__('Account details successfully deleted from %s','twitter-auto-publish'),$twap_smapsolution_var),
	    'html2' => sprintf(__('In-active Twitter account successfully deleted from %s','twitter-auto-publish'),$twap_smapsolution_var),
	    'html3' => sprintf(__('Please connect your %s member account','twitter-auto-publish'),$twap_xyzscripts_var),
	    'html4' => __('Thank you for enabling backlink !','twitter-auto-publish')
	));
	wp_register_style('xyz_twap_style', plugins_url('twitter-auto-publish/css/style.css'));
	wp_enqueue_style('xyz_twap_style');
}

add_action("admin_enqueue_scripts","xyz_twap_add_admin_scripts");

function xyz_twap_menu()
{
	add_menu_page('Twitter Auto Publish - Manage settings', 'WP Twitter Auto Publish', 'manage_options', 'twitter-auto-publish-settings', 'xyz_twap_settings',plugin_dir_url( XYZ_TWAP_PLUGIN_FILE ) . 'images/twap.png');
	$page=add_submenu_page('twitter-auto-publish-settings', 'Twitter Auto Publish - Manage settings', __('Settings','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-settings' ,'xyz_twap_settings'); // 8 for admin
	if(get_option('xyz_twap_xyzscripts_hash_val')!='' && get_option('xyz_twap_xyzscripts_user_id')!='' && get_option('xyz_twap_smapsoln_userid') > 0)
	    add_submenu_page('twitter-auto-publish-settings', 'Twitter Auto Publish - Manage Authorizations',  __('Manage Authorizations','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-manage-authorizations' ,'xyz_twap_manage_authorizations');
	add_submenu_page('twitter-auto-publish-settings', 'Twitter Auto Publish - Logs', __('Logs','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-log' ,'xyz_twap_logs');
	add_submenu_page('twitter-auto-publish-settings', 'Twitter Auto Publish - Queue', __('发布队列','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-queue' ,'xyz_twap_queue_page'); // 添加队列管理页面
	add_submenu_page('twitter-auto-publish-settings', 'Twitter Auto Publish - Maintenance', __('维护工具','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-maintenance' ,'xyz_twap_maintenance_page'); // 添加维护工具页面
	add_submenu_page('twitter-auto-publish-settings', 'Twitter Auto Publish - About', __('About','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-about' ,'xyz_twap_about'); // 8 for admin
	add_submenu_page('twitter-auto-publish-settings','Twitter Auto Publish - Suggest Feature', __('Suggest a Feature','twitter-auto-publish'), 'manage_options', 'twitter-auto-publish-suggest-features' ,'xyz_twap_suggest_feature');
}


function xyz_twap_settings()
{
	$_POST = stripslashes_deep($_POST);
	$_GET = stripslashes_deep($_GET);	
	$_POST = xyz_trim_deep($_POST);
	$_GET = xyz_trim_deep($_GET);
	
	require( dirname( __FILE__ ) . '/header.php' );
	require( dirname( __FILE__ ) . '/settings.php' );
	require( dirname( __FILE__ ) . '/footer.php' );
}

function xyz_twap_manage_authorizations()
{
	require( dirname( __FILE__ ) . '/header.php' );
	require( dirname( __FILE__ ) . '/manage-auth.php' );
	require( dirname( __FILE__ ) . '/footer.php' );
}

function xyz_twap_about()
{
	require( dirname( __FILE__ ) . '/header.php' );
	require( dirname( __FILE__ ) . '/about.php' );
	require( dirname( __FILE__ ) . '/footer.php' );
}


function xyz_twap_suggest_feature()
{
	require( dirname( __FILE__ ) . '/header.php' );
	require( dirname( __FILE__ ) . '/suggest_feature.php' );
	require( dirname( __FILE__ ) . '/footer.php' );
}
function xyz_twap_logs()
{
	$_POST = stripslashes_deep($_POST);
	$_GET = stripslashes_deep($_GET);
	$_POST = xyz_trim_deep($_POST);
	$_GET = xyz_trim_deep($_GET);

	require( dirname( __FILE__ ) . '/header.php' );
	require( dirname( __FILE__ ) . '/logs.php' );
	require( dirname( __FILE__ ) . '/footer.php' );
}
add_action('wp_head', 'xyz_smap_insert_twitter_card');
function xyz_smap_insert_twitter_card(){
    global $post;
    $xyz_twap_tw_enforce_twitter_cards=get_option('xyz_twap_tw_enforce_twitter_cards');
    if (empty($post))
        $post=get_post();
        if (!empty($post) && ( $xyz_twap_tw_enforce_twitter_cards==1 ) )
        {
            $postid= $post->ID;
            $name='';$excerpt='';$attachmenturl='';
            if(isset($postid) && $postid>0 && isset($_SERVER["HTTP_USER_AGENT"]))
            { 
                $get_post_meta_insert_twitter_card=0;
                $get_post_meta_future_data=get_post_meta($postid,"xyz_twap_future_to_publish",true);
                
                $get_post_meta_insert_twitter_card=get_post_meta($postid,"xyz_twap_insert_twitter_card",true);
                if (!empty($get_post_meta_future_data) && ( $xyz_twap_tw_enforce_twitter_cards==1 ) && (strpos($_SERVER["HTTP_USER_AGENT"], "Twitterbot") !== false))
                { 
                    $xyz_smap_apply_filters=get_option('xyz_smap_apply_filters');
                        $ar2=explode(",",$xyz_smap_apply_filters);
                        $excerpt = $post->post_excerpt;
                        if(in_array(2, $ar2))
                            $excerpt = apply_filters('the_excerpt', $excerpt);
                            $excerpt = html_entity_decode($excerpt, ENT_QUOTES, get_bloginfo('charset'));
                            $excerpt = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', "", $excerpt);
                            if($excerpt=="")
                            {
                                $content = $post->post_content;
                                if(in_array(1, $ar2))
                                    $content = apply_filters('the_content', $content);
                                    if($content!="")
                                    {
                                        $content1=$content;
                                        $content1=strip_tags($content1);
                                        $content1=strip_shortcodes($content1);
                                        $content1 = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', "", $content1);
                                        $content1=  preg_replace("/\\[caption.*?\\].*?\\[.caption\\]/is", "", $content1);
                                        $content1 = preg_replace('/\[.+?\]/', '', $content1);
                                        $excerpt=implode(' ', array_slice(explode(' ', $content1), 0, 50));
                                    }
                            }
                            else
                            {
                                $excerpt=strip_tags($excerpt);
                                $excerpt=strip_shortcodes($excerpt);
                            } //print_r($excerpt);die;
                            $excerpt=str_replace("&nbsp;","",$excerpt);
                            //print_r($excerpt);die;
                            $name = $post->post_title;//print_r($name);die;
                            if(in_array(3, $ar2))
                                $name = apply_filters('the_title', $name,$postid);
                                $name = html_entity_decode($name, ENT_QUOTES, get_bloginfo('charset'));//print_r($name);die;
                                $name=strip_tags($name);
                                $name=strip_shortcodes($name);
                                $attachmenturl=xyz_twap_getimage($postid, $post->post_content);
                                if(is_array($attachmenturl) && isset($attachmenturl['image_url']) && $attachmenturl['image_url']!='')
                                    $attachmenturl=$attachmenturl['image_url'];//print_r($name);die;**/
                }
                if (($get_post_meta_insert_twitter_card==1) && strpos($_SERVER["HTTP_USER_AGENT"], "Twitterbot") !== false && ($xyz_twap_tw_enforce_twitter_cards==1))
                    {
                        echo '<meta name="twitter:card" content="summary_large_image" />';
                        if(!empty( $name ))
                            echo '<meta name="twitter:title" content="'.$name.'" />';
                            if (!empty($excerpt))
                                echo '<meta name="twitter:description" content="'.$excerpt.'" />';
                                    if(!empty($attachmenturl))
                                        echo '<meta name="twitter:image" content="'.$attachmenturl.'" />';
                    }
                }
            }
        }
        add_filter( 'cron_schedules', 'xyz_twap_custom_cron_interval' );

        if ( ! wp_next_scheduled( 'xyz_twap_tw_auto_reauth' ) ) {
            wp_schedule_event( time(), 'twap_reauth_every_two_hours', 'xyz_twap_tw_auto_reauth' );
        }
        if(get_option('xyz_twap_tw_app_sel_mode')==2){
            require_once (dirname(__FILE__) . '/../api/twitter.php');
            add_action( 'xyz_twap_tw_auto_reauth', 'xyz_twap_twitter_auth2_reauth' );
        }

function xyz_twap_admin_settings()
{
	// ... existing code ...
	
	// 添加频率限制设置
	echo '<tr valign="top">
			<th scope="row">推文发布间隔（分钟）</th>
			<td>
				<input type="number" name="xyz_twap_post_interval" value="'.esc_attr(get_option('xyz_twap_post_interval', 15)).'" min="6" step="1" />
				<p class="description">设置两条推文之间的最小间隔时间（分钟）。推荐设置为15分钟或以上，以避免被Twitter限制。</p>
			</td>
		</tr>';

	// ... existing code ...
}

function xyz_twap_settings_save()
{
	// ... existing code ...
	
	// 保存频率限制设置
	if(isset($_POST['xyz_twap_post_interval']))
	{
		$interval = intval($_POST['xyz_twap_post_interval']);
		if($interval >= 6) // 至少6分钟
		{
			update_option('xyz_twap_post_interval', $interval);
		}
		else
		{
			add_settings_error('xyz_twap_post_interval', 'xyz_twap_post_interval_error', '发布间隔时间必须至少为6分钟！');
		}
	}
	
	// ... existing code ...
}

/**
 * 维护工具页面
 */
function xyz_twap_maintenance_page() {
    echo "<h1>Twitter自动发布插件维护工具</h1>";

    // 显示当前设置
    $current_interval = get_option('xyz_twap_post_interval', 15);
    echo "<p>当前发布间隔设置：{$current_interval} 分钟</p>";

    // 检查最后发布时间
    $last_post_time = get_option('xyz_twap_last_tweet_time', 0);
    if ($last_post_time > 0) {
        echo "<p>最后发布时间：" . date('Y-m-d H:i:s', $last_post_time) . "</p>";
        echo "<p>距现在时间：" . round((time() - $last_post_time) / 60) . " 分钟</p>";
    } else {
        echo "<p>尚无发布记录</p>";
    }

    // 处理表单提交
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'update_interval') {
            $new_interval = intval($_POST['interval']);
            if ($new_interval >= 6) {
                update_option('xyz_twap_post_interval', $new_interval);
                echo "<div class='updated'><p>已更新发布间隔为: {$new_interval} 分钟</p></div>";
            } else {
                echo "<div class='error'><p>发布间隔必须至少为6分钟</p></div>";
            }
        } elseif ($_POST['action'] == 'reset_last_time') {
            update_option('xyz_twap_last_tweet_time', 0);
            echo "<div class='updated'><p>已重置最后发布时间</p></div>";
        } elseif ($_POST['action'] == 'clear_log') {
            $log_file = WP_CONTENT_DIR . '/twitter-rate-limit.log';
            if (file_exists($log_file)) {
                file_put_contents($log_file, '');
                echo "<div class='updated'><p>已清空日志文件</p></div>";
            } else {
                echo "<div class='updated'><p>日志文件不存在</p></div>";
            }
        } elseif ($_POST['action'] == 'check_cron') {
            // 检查并修复定时任务
            $timestamp = wp_next_scheduled('xyz_twap_process_queue');
            if ($timestamp) {
                echo "<div class='updated'><p>定时任务已存在，计划执行时间：" . date('Y-m-d H:i:s', $timestamp) . "</p></div>";
            } else {
                wp_schedule_event(time() + 60, 'twap_minute', 'xyz_twap_process_queue');
                echo "<div class='updated'><p>已重新计划定时任务，将在1分钟后执行</p></div>";
            }
        }
    }

    // 显示表单
    ?>

    <div class="wrap">
        <div style="width: 99%;">
            <form method="post" action="">
                <h2>设置发布间隔</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">新的发布间隔（分钟）</th>
                        <td>
                            <input type="hidden" name="action" value="update_interval">
                            <input type="number" name="interval" value="<?php echo $current_interval; ?>" min="6" class="regular-text">
                            <p class="description">推荐设置为15分钟或以上，以避免被Twitter限制。</p>
                        </td>
                    </tr>
                </table>
                <p class="submit">
                    <input type="submit" class="button-primary" value="更新间隔">
                </p>
            </form>

            <form method="post" action="">
                <h2>重置最后发布时间</h2>
                <p>如果你想立即发布队列中的内容，可以重置最后发布时间</p>
                <input type="hidden" name="action" value="reset_last_time">
                <p class="submit">
                    <input type="submit" class="button" value="重置时间" onclick="return confirm('确定要重置最后发布时间吗？这将允许立即发布队列中的下一条推文。')">
                </p>
            </form>

            <form method="post" action="">
                <h2>清空日志文件</h2>
                <input type="hidden" name="action" value="clear_log">
                <p class="submit">
                    <input type="submit" class="button" value="清空日志" onclick="return confirm('确定要清空日志文件吗？')">
                </p>
            </form>

            <form method="post" action="">
                <h2>检查/修复定时任务</h2>
                <input type="hidden" name="action" value="check_cron">
                <p class="submit">
                    <input type="submit" class="button" value="检查定时任务">
                </p>
            </form>
        </div>
    </div>
    <?php
        }
