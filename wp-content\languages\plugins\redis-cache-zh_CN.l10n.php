<?php
return ['x-generator'=>'GlotPress/4.0.1','translation-revision-date'=>'2023-11-17 01:10:25+0000','plural-forms'=>'nplurals=1; plural=0;','project-id-version'=>'Plugins - Redis Object Cache - Stable (latest release)','language'=>'zh_CN','messages'=>['Object cache drop-in is not writable.'=>'对象缓存 Drop-in（强化扩展）不可写。','File modifications are not allowed.'=>'不允许修改文件。','Check out %s'=>'查看 %s','Need more performance and reliability?'=>'需要更高的性能和可靠性？','To disable Redis, delete the %1$s file in the %2$s directory.'=>'要禁用 Redis，请删除 %2$s 目录中的 %1$s 文件。','Is the Redis server running?'=>'Redis 服务器是否正在运行？','Is the correct Redis host and port set?'=>'Redis 主机和端口设置是否正确？','WordPress is unable to establish a connection to Redis. This means that the connection information in your %s file are incorrect, or that the Redis server is not reachable.'=>'WordPress 无法建立与 Redis 的连接。这意味着您的 %s 文件中的连接信息不正确，或者无法访问 Redis 服务器。','Unreachable'=>'无法访问','Reachable'=>'可访问','Redis:'=>'Redis：','Redis is unreachable:'=>'Redis 无法访问：','If you need help, please read the <a href="%s">installation instructions</a>.'=>'如果您需要帮助，请阅读<a href="%s">安装说明</a>。','Error establishing a Redis connection'=>'建立 Redis 连接时出错','Object cache drop-in could not be updated. Redis server is unreachable: %s'=>'无法更新对象缓存插入。 Redis 服务器无法访问：%s','Object cache could not be enabled. Redis server is unreachable: %s'=>'无法启用对象缓存。 Redis 服务器无法访问：%s','(Current page) Hit Ratio: %1$s%%, Hits %2$s, Misses: %3$s, Size: %4$s'=>'（当前页）命中率：%1$s%%，命中 %2$s，未命中：%3$s，大小：%4$s','Status: %s'=>'状态：%s','Flushing cache...'=>'刷新缓存...','Drop-in is outdated'=>'Drop-in（加载项） 已过时','Not enabled'=>'未启用','The Object Cache Pro plugin appears to be installed and should be used. You can safely <a href="%s">uninstall Redis Object Cache</a>.'=>'Object Cache Pro 插件似乎已安装并且应该使用。您可以安全地<a href="%s">卸载 Redis 对象缓存</a>。','Writeable'=>'可写入','Not writeable'=>'不可写','Filesystem:'=>'文件系统：','The total amount of commands sent to Redis.'=>'发送到 Redis 的命令总数。','The hit/miss ratio of cache data that was already cached.'=>'已缓存的缓存数据的命中/未命中率。','The total amount of bytes that was retrieved from Redis.'=>'从 Redis 检索的字节总数。','The total amount of time (in milliseconds) it took Redis to return cache data.'=>'Redis 返回缓存数据所花费的总时间（以毫秒为单位）。','Copied!'=>'已复制！','Copy diagnostics to clipboard'=>'将诊断复制到剪贴板','Content directory is not writable.'=>'内容目录不可写。','Object Cache Pro!'=>'对象缓存专业版！','verbUpgrade to Pro'=>'升级到专业版','Cache prefetching and analytics'=>'缓存预取和分析','Optimized for WooCommerce, Jetpack & Yoast SEO'=>'针对 WooCommerce、Jetpack 和 Yoast SEO 进行了优化','Health checks via WordPress & WP CLI'=>'通过 WordPress 和 WP CLI 进行健康检查','Secure connections with TLS'=>'使用 TLS 保护连接','Fully unit tested (100% code coverage)'=>'完全单元测试（100% 代码覆盖率）','Easy debugging & logging'=>'轻松调试和记录','Faster serialization and compression'=>'更快的序列化和压缩','100% WordPress API compliant'=>'100% WordPress API 兼容','Rewritten for raw performance'=>'为原始性能重写','%s are disabled for this site.'=>'该站点已禁用 %s。','Drop-in is invalid'=>'Drop-in（加载项）  是无效的','https://objectcache.pro'=>'https://objectcache.pro','Copied test file couldn’t be deleted.'=>'无法删除复制的测试文件。','Couldn’t verify test file contents.'=>'无法验证测试文件内容。','Copied test file doesn’t exist.'=>'复制的测试文件不存在。','Failed to copy test file.'=>'未能复制测试文件。','Test file exists, but couldn’t be deleted.'=>'测试文件存在，但无法删除。','Object cache file doesn’t exist.'=>'对象缓存文件不存在。','Could not initialize filesystem.'=>'无法初始化文件系统。','Username:'=>'用户名：','A foreign object cache drop-in was found. To use Redis for object caching, please <a href="%s">enable the drop-in</a>.'=>'找到一个外部对象缓存加载项。要使用 Redis 进行对象缓存，请<a href="%s">启用该加载项</a>。','Enable object cache to collect data.'=>'启用对象缓存来收集数据。','Not enough data collected, yet.'=>'尚未收集足够的数据。','Settings'=>'设置','Metrics'=>'指标','WordPress is using a foreign object cache drop-in and Redis Object Cache is not being used. Use WP CLI or go to "Settings -> Redis" to enable drop-in.'=>'WordPress 正在使用外部对象缓存插件，并且未使用 Redis 对象缓存。使用 WP CLI 或转到“设置 -> Redis”以启用 drop-in（加载项）。','The Redis Object Cache drop-in is not installed. Use WP CLI or go to "Settings -> Redis" to enable drop-in.'=>'未安装 Redis 对象缓存加载项。使用 WP CLI 或转到“设置 -> Redis”以启用 drop-in(加载项)。','Object Cache'=>'对象缓存','A foreign object cache drop-in was found. To use Redis for object caching, run: `wp redis update-dropin`.'=>'找到一个外部对象缓存加载项。要使用 Redis 进行对象缓存，请运行：`wp redis update-dropin`。','Redis Version:'=>'Redis 版本：','%sms'=>'%s 毫秒','Retry Interval:'=>'重试间隔：','Read Timeout:'=>'读取超时：','%ss'=>'%s 秒','Connection Timeout:'=>'连接超时：','Shards:'=>'碎片：','Cluster:'=>'集群：','Connection'=>'连接','Metadata'=>'Meta数据','Unflushable Groups'=>'不可刷新组','Non-persistent Groups'=>'非持久性组','Errors'=>'错误','Size'=>'大小','Misses'=>'未命中','Hits'=>'命中','Hit Ratio'=>'命中率','Calls'=>'调用','Ratio'=>'比率','Bytes'=>'字节','Time'=>'时间','Your site meets the system requirements for the Pro version.'=>'您的站点满足专业版的系统要求。','Resources'=>'资源','Retrieved %1$d objects (%2$s) from Redis using %3$s.'=>'使用 %3$s 从 Redis 检索了 %1$d 个对象 (%2$s)。','The current version of the PhpRedis extension (%s) is too old. PhpRedis 3.1.1 or newer is required.'=>'PhpRedis 扩展 (%s) 的当前版本太旧。需要 PhpRedis 3.1.1 或更高版本。','The PhpRedis extension is not installed.'=>'PhpRedis 扩展未安装。','The current version of PHP (%s) is too old. PHP 7.2 or newer is required.'=>'PHP (%s) 的当前版本太旧。需要 PHP 7.2 或更高版本。','Your site <i>does not</i> meet the requirements for the Pro version:'=>'您的网站<i>不满足</i>专业版的要求：','This doesn’t appear to be a valid number.'=>'这似乎不是一个有效的数字。','Object Cache Pro is a <u>business class</u> object cache that’s highly-optimized for WooCommerce to provide true reliability, peace of mind and faster load times for your store. <a style="color: #bb77ae;" href="%s">Learn more »</a>'=>'Object Cache Pro 是一款<u>商业级</u>对象缓存，针对 WooCommerce 进行了高度优化，可为您的商店提供真正的可靠性、安心和更快的加载时间。 <a style="color: #bb77ae;" href="%s">了解更多 »</a>','Object Cache Pro + WooCommerce = ❤️'=>'Object Cache Pro + WooCommerce = ❤️','A <u>business class</u> object cache backend. Truly reliable, highly-optimized and fully customizable, with a <u>dedicated engineer</u> when you most need it. <a href="%s">Learn more »</a>'=>'一个<u>商业化</u>对象缓存后端。真正可靠、高度优化和完全可定制，在您最需要的时候配备一名<u>专业工程师</u>。<a href="%s">了解更多信息 »</a>','Learn more'=>'了解更多','<strong>A business class object cache backend.</strong> Truly reliable, highly-optimized and fully customizable, with a <u>dedicated engineer</u> when you most need it.'=>'<strong>商业级对象缓存后端。</strong>真正可靠，高度优化和完全可定制，<u>专业的工程师</u>都需要它。','Global Groups'=>'全局组','Status'=>'状态','Diagnostics'=>'诊断','Object cache drop-in could not be updated.'=>'无法更新对象缓存加载项。','Updated object cache drop-in and enabled Redis object cache.'=>'更新了对象缓存加载项并启用了 Redis 对象缓存。','No object cache drop-in found.'=>'未找到对象缓存加载项。','Redis object cache already enabled.'=>'Redis 对象缓存已启用。','A persistent object cache backend powered by Redis. Supports Predis, PhpRedis, Relay, replication, sentinels, clustering and WP-CLI.'=>'由 Redis 提供支持的持久对象缓存后端。支持 Predis、PhpRedis、Relay、replication、 sentinels、clustering和 WP-CLI。','Yes'=>'是','Servers:'=>'服务器：','Overview'=>'概览','Disabled'=>'未启用','Status:'=>'状态：','Till Krüss'=>'Till Krüss','https://wordpress.org/plugins/redis-cache/'=>'https://wordpress.org/plugins/redis-cache/','Object cache could not be flushed.'=>'无法刷新对象缓存。','Object cache flushed.'=>'对象缓存已刷新。','Object cache could not be disabled.'=>'无法禁用对象缓存。','Object cache disabled.'=>'对象缓存已禁用','Object cache could not be enabled.'=>'无法启用对象缓存。','Object cache enabled.'=>'启用对象缓存。','The Redis object cache drop-in is outdated. Please <a href="%s">update the drop-in</a>.'=>'Redis 对象缓存插件加载项已过时。请<a href="%s">更新插件</a>。','Unknown'=>'未知','Not connected'=>'未连接','Connected'=>'已连接','Redis'=>'Redis','Disable Object Cache'=>'禁用对象缓存','Enable Object Cache'=>'启用对象缓存','Flush Cache'=>'刷新缓存','Max. TTL:'=>'最大TTL值（生存时间）：','Key Prefix:'=>'密钥前缀：','Password:'=>'密码：','Database:'=>'数据库：','Port:'=>'端口：','Host:'=>'主机：','Client:'=>'客户端：','Redis Object Cache'=>'Redis Object Cache']];