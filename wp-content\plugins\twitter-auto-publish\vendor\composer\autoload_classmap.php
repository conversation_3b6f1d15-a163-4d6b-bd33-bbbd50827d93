<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    '<PERSON>\\TwitterOAuth\\Config' => $vendorDir . '/abraham/twitteroauth/src/Config.php',
    '<PERSON>\\TwitterOAuth\\Consumer' => $vendorDir . '/abraham/twitteroauth/src/Consumer.php',
    '<PERSON>\\TwitterOAuth\\HmacSha1' => $vendorDir . '/abraham/twitteroauth/src/HmacSha1.php',
    '<PERSON>\\TwitterOAuth\\Request' => $vendorDir . '/abraham/twitteroauth/src/Request.php',
    '<PERSON>\\TwitterOAuth\\Response' => $vendorDir . '/abraham/twitteroauth/src/Response.php',
    '<PERSON>\\TwitterOAuth\\SignatureMethod' => $vendorDir . '/abraham/twitteroauth/src/SignatureMethod.php',
    '<PERSON>\\TwitterOAuth\\Token' => $vendorDir . '/abraham/twitteroauth/src/Token.php',
    '<PERSON>\\TwitterOAuth\\TwitterOAuth' => $vendorDir . '/abraham/twitteroauth/src/TwitterOAuth.php',
    '<PERSON>\\TwitterOAuth\\TwitterOAuthException' => $vendorDir . '/abraham/twitteroauth/src/TwitterOAuthException.php',
    'Abraham\\TwitterOAuth\\Util' => $vendorDir . '/abraham/twitteroauth/src/Util.php',
    'Abraham\\TwitterOAuth\\Util\\JsonDecoder' => $vendorDir . '/abraham/twitteroauth/src/Util/JsonDecoder.php',
    'Composer\\CaBundle\\CaBundle' => $vendorDir . '/composer/ca-bundle/src/CaBundle.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
);
