# 小程序API优化插件

这个WordPress插件为微信小程序提供了优化的API端点，可以显著提高小程序的性能。

## 功能特点

- 提供优化的分类列表API端点
- 提供优化的分类文章API端点
- 实现数据缓存，减少服务器负载
- 优化数据结构，减少传输数据量
- 在服务器端处理图片URL，避免客户端解析HTML

## 安装方法

1. 下载插件文件夹
2. 上传到WordPress网站的`wp-content/plugins/`目录
3. 在WordPress管理后台激活"小程序API优化"插件

## API端点说明

### 1. 获取分类列表

**请求URL**: `/wp-json/miniapp/v1/categories`

**请求方法**: GET

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "name": "科技",
      "slug": "tech",
      "count": 25,
      "description": "科技相关文章",
      "icon": "/static/image/category/tech.png"
    },
    // 更多分类...
  ]
}
```

### 2. 获取分类文章

**请求URL**: `/wp-json/miniapp/v1/category-posts/{slug}`

**请求方法**: GET

**请求参数**:
- `page`: 页码，默认为1
- `per_page`: 每页数量，默认为10

**响应示例**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 123,
      "title": "文章标题",
      "excerpt": "文章摘要...",
      "image": "https://example.com/image.jpg",
      "date": "2023-02-24",
      "downloads": 1500,
      "price": 0.01
    },
    // 更多文章...
  ],
  "hasMore": true,
  "total": 25,
  "totalPages": 3,
  "message": "success"
}
```

### 3. 获取文章列表

**请求URL**: `/wp-json/miniapp/v1/posts`

**请求方法**: GET

**请求参数**:
- `page`: 页码，默认为1
- `per_page`: 每页数量，默认为10
- `category`: 分类ID，可选
- `search`: 搜索关键词，可选

**响应示例**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 123,
      "title": "文章标题",
      "excerpt": "文章摘要...",
      "image": "https://example.com/image.jpg",
      "date": "2023-02-24",
      "downloads": 1500,
      "price": 0.01,
      "isPaid": false,
      "categoryId": 5,
      "category": "分类名称",
      "categorySlug": "category-slug"
    },
    // 更多文章...
  ],
  "hasMore": true,
  "total": 25,
  "totalPages": 3,
  "message": "success"
}
```

### 4. 获取文章详情

**请求URL**: `/wp-json/miniapp/v1/posts/{id}`

**请求方法**: GET

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 123,
    "title": "文章标题",
    "content": "文章内容...",
    "excerpt": "文章摘要...",
    "image": "https://example.com/image.jpg",
    "date": "2023-02-24",
    "downloads": 1500,
    "price": 0.01,
    "isPaid": false,
    "categoryId": 5,
    "category": "分类名称",
    "categorySlug": "category-slug",
    "allCategories": [
      {
        "id": 5,
        "name": "分类名称",
        "slug": "category-slug"
      }
    ]
  }
}
```

## 使用方法

在小程序的云函数中，使用这些API端点替代原有的WordPress REST API调用。

## 注意事项

- 插件使用WordPress的transient API进行缓存，分类列表缓存12小时，文章列表缓存30分钟
- 如需清除缓存，可以在WordPress管理后台重新激活插件 