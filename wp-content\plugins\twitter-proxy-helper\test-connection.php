<?php
/**
 * Twitter连接测试脚本
 * 用于测试修复后的Twitter API连接
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html>
<head>
    <title>Twitter连接测试 - 修复验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .test-result.success { border-left-color: green; background: #f0fff0; }
        .test-result.error { border-left-color: red; background: #fff0f0; }
        .test-result.warning { border-left-color: orange; background: #fff8f0; }
    </style>
</head>
<body>
    <h1>🔧 Twitter连接测试 - 修复验证</h1>
    <p>测试修复后的Twitter API连接功能</p>
    
    <div class="section">
        <h2>1. 代理配置测试</h2>
        <?php
        // 测试代理函数
        if (function_exists('twitter_proxy_get_proxy')) {
            $proxy = twitter_proxy_get_proxy();
            echo '<div class="test-result success">✅ 代理函数正常: ' . esc_html($proxy) . '</div>';
        } else {
            echo '<div class="test-result error">❌ 代理函数不存在</div>';
        }
        
        // 检查全局代理变量
        if (isset($GLOBALS['xyz_twap_proxy'])) {
            echo '<div class="test-result success">✅ 全局代理变量已设置</div>';
            echo '<pre>' . print_r($GLOBALS['xyz_twap_proxy'], true) . '</pre>';
        } else {
            echo '<div class="test-result warning">⚠️ 全局代理变量未设置</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>2. CURL常量测试</h2>
        <?php
        $curl_constants = [
            'CURLOPT_PROXY' => CURLOPT_PROXY,
            'CURLOPT_PROXYPORT' => CURLOPT_PROXYPORT,
            'CURLOPT_PROXYTYPE' => CURLOPT_PROXYTYPE,
            'CURLPROXY_SOCKS5_HOSTNAME' => CURLPROXY_SOCKS5_HOSTNAME,
            'CURL_SSLVERSION_TLSv1_2' => CURL_SSLVERSION_TLSv1_2,
            'CURL_IPRESOLVE_V4' => CURL_IPRESOLVE_V4
        ];
        
        foreach ($curl_constants as $name => $value) {
            echo '<div class="test-result success">✅ ' . $name . ' = ' . $value . '</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>3. 代理连接测试</h2>
        <?php
        // 测试代理连接到Twitter API
        $test_url = 'https://api.twitter.com/2/openapi.json';
        echo '<p>测试URL: ' . $test_url . '</p>';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_PROXY, '************');
        curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
        curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        
        $start = microtime(true);
        $response = curl_exec($ch);
        $time = round((microtime(true) - $start) * 1000);
        $error = curl_error($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        rewind($verbose);
        $verbose_log = stream_get_contents($verbose);
        curl_close($ch);
        
        if ($error) {
            echo '<div class="test-result error">❌ 连接失败: ' . esc_html($error) . '</div>';
            echo '<div class="test-result warning">详细日志:</div>';
            echo '<pre>' . esc_html($verbose_log) . '</pre>';
        } else {
            echo '<div class="test-result success">✅ 连接成功! HTTP状态码: ' . $http_code . ' (响应时间: ' . $time . 'ms)</div>';
            if ($http_code == 200) {
                echo '<div class="test-result success">✅ API响应正常</div>';
            } else {
                echo '<div class="test-result warning">⚠️ HTTP状态码: ' . $http_code . '</div>';
            }
        }
        ?>
    </div>
    
    <div class="section">
        <h2>4. TwitterOAuth类测试</h2>
        <?php
        if (class_exists('Abraham\TwitterOAuth\TwitterOAuth')) {
            echo '<div class="test-result success">✅ TwitterOAuth类已加载</div>';
            
            // 测试创建实例
            try {
                $connection = new \Abraham\TwitterOAuth\TwitterOAuth('test', 'test');
                echo '<div class="test-result success">✅ TwitterOAuth实例创建成功</div>';
                
                // 测试代理设置
                $proxy_config = [
                    CURLOPT_PROXY => '************',
                    CURLOPT_PROXYPORT => 7890,
                    CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5_HOSTNAME
                ];
                
                $connection->setProxy($proxy_config);
                echo '<div class="test-result success">✅ 代理设置成功</div>';
                
            } catch (Exception $e) {
                echo '<div class="test-result error">❌ TwitterOAuth测试失败: ' . esc_html($e->getMessage()) . '</div>';
            }
        } else {
            echo '<div class="test-result error">❌ TwitterOAuth类未找到</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>5. 错误处理测试</h2>
        <?php
        if (function_exists('twitter_proxy_handle_api_error')) {
            echo '<div class="test-result success">✅ 错误处理函数已加载</div>';
            
            // 测试错误处理
            $test_error = new WP_Error('connection_timeout', 'Connection timed out');
            $is_retryable = twitter_proxy_handle_api_error($test_error, '测试');
            
            if ($is_retryable) {
                echo '<div class="test-result success">✅ 超时错误识别为可重试</div>';
            } else {
                echo '<div class="test-result warning">⚠️ 超时错误未识别为可重试</div>';
            }
        } else {
            echo '<div class="test-result error">❌ 错误处理函数未找到</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>6. 修复总结</h2>
        <div class="test-result success">
            <h3>✅ 已修复的问题:</h3>
            <ul>
                <li>CURLINFO_PROXY常量错误 - 已修复为正确的常量</li>
                <li>img_size_bytes未定义变量 - 已添加初始化</li>
                <li>WP_Error对象错误处理 - 已添加is_wp_error检查</li>
                <li>连接超时问题 - 已增加超时时间和重试机制</li>
                <li>代理配置优化 - 已添加更多CURL选项</li>
            </ul>
        </div>
        
        <div class="test-result warning">
            <h3>⚠️ 建议:</h3>
            <ul>
                <li>清除WordPress缓存以确保修复生效</li>
                <li>监控错误日志查看是否还有问题</li>
                <li>测试实际的Twitter发布功能</li>
            </ul>
        </div>
    </div>
    
    <p><strong>测试完成时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
</body>
</html>
