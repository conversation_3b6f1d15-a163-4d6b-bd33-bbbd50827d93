<?php
/**
 * 付费内容处理模块
 * 
 * 提供付费文章处理和标记功能
 */

// 禁止直接访问
if (!defined('WPINC')) {
    die;
}

/**
 * 检查文章是否为付费内容
 * 
 * @param int $post_id 文章ID
 * @return array|bool 付费信息或false
 */
function wp_telegram_is_paid_content($post_id) {
    // 支持子比主题的付费系统
    $posts_zibpay = get_post_meta($post_id, 'posts_zibpay', true);
    if (!empty($posts_zibpay) && is_array($posts_zibpay) && isset($posts_zibpay['pay_type']) && $posts_zibpay['pay_type'] !== 'no') {
        wp_telegram_log('文章ID ' . $post_id . ' 是付费文章', 'debug');
        return $posts_zibpay;
    }
    
    // 支持其他常见付费插件的检测方法
    // 如：Erphpdown, WP Paid Memberships Pro等
    
    // 可以在这里添加更多付费插件的检测逻辑
    
    return false;
}

/**
 * 获取付费信息文本
 * 
 * @param array $pay_info 付费信息数组
 * @return string 格式化的付费信息
 */
function wp_telegram_get_paid_content_text($pay_info) {
    $text = "\n\n💰 *付费资源* ";
    
    if (isset($pay_info['pay_modo']) && $pay_info['pay_modo'] === 'points') {
        // 积分支付
        $points_price = !empty($pay_info['points_price']) ? (int)$pay_info['points_price'] : 0;
        if ($points_price > 0) {
            $text .= ": " . $points_price . "积分";
        }
    } else {
        // 金额支付
        $pay_price = !empty($pay_info['pay_price']) ? round((float)$pay_info['pay_price'], 2) : 0;
        if ($pay_price > 0) {
            $text .= ": ¥" . $pay_price;
        }
    }
    
    // 添加付费类型标签
    if (isset($pay_info['pay_type'])) {
        switch ($pay_info['pay_type']) {
            case 'pay_download':
                $text .= " (下载)";
                break;
            case 'pay_read':
                $text .= " (阅读)";
                break;
            case 'pay_parts':
                $text .= " (部分内容)";
                break;
            case 'vip_free':
                $text .= " (VIP免费)";
                break;
            case 'vip1_free':
                $text .= " (VIP1免费)";
                break;
            case 'vip2_free':
                $text .= " (VIP2免费)";
                break;
        }
    }
    
    return $text;
}

/**
 * 提取文章底部的网盘链接并识别类型
 * 
 * @param string $content 文章内容
 * @return array|bool 包含链接和类型的数组或false
 */
function wp_telegram_extract_pan_link($content) {
    // 截取文章末尾部分（最后1000个字符）进行检查
    $content_end = mb_substr($content, -1000);
    
    // 支持常见网盘域名及对应图标
    $pan_types = array(
        // 百度网盘
        array(
            'pattern' => '/https?:\/\/(?:pan|yun)\.baidu\.com\/s\/[a-zA-Z0-9_-]+/i',
            'name' => '百度网盘',
            'icon' => '🔵'
        ),
        // 夸克网盘
        array(
            'pattern' => '/https?:\/\/pan\.quark\.cn\/s\/[a-zA-Z0-9]+/i',
            'name' => '夸克网盘',
            'icon' => '🟠'
        ),
        // 阿里云盘
        array(
            'pattern' => '/https?:\/\/(?:www\.)?aliyundrive\.com\/s\/[a-zA-Z0-9]+/i',
            'name' => '阿里云盘',
            'icon' => '🔷'
        ),
        // 蓝奏云
        array(
            'pattern' => '/https?:\/\/(?:www\.)?lanzou[a-z]*\.com\/[a-zA-Z0-9_-]+/i',
            'name' => '蓝奏云',
            'icon' => '🔹'
        ),
        // 天翼云盘
        array(
            'pattern' => '/https?:\/\/cloud\.189\.cn\/t\/[a-zA-Z0-9]+/i',
            'name' => '天翼云盘',
            'icon' => '🔸'
        ),
        // 微云
        array(
            'pattern' => '/https?:\/\/share\.weiyun\.com\/[a-zA-Z0-9]+/i',
            'name' => '腾讯微云',
            'icon' => '🟢'
        ),
        // 坚果云
        array(
            'pattern' => '/https?:\/\/www\.jianguoyun\.com\/p\/[a-zA-Z0-9_-]+/i',
            'name' => '坚果云',
            'icon' => '🌰'
        ),
        // 123云盘
        array(
            'pattern' => '/https?:\/\/(?:www\.)?123pan\.com\/s\/[a-zA-Z0-9-]+/i',
            'name' => '123云盘',
            'icon' => '📊'
        ),
        // 迅雷云盘
        array(
            'pattern' => '/https?:\/\/pan\.xunlei\.com\/s\/[a-zA-Z0-9]+/i',
            'name' => '迅雷云盘',
            'icon' => '⚡'
        ),
        // 城通网盘
        array(
            'pattern' => '/https?:\/\/(?:www\.)?ctfile\.com\/[a-zA-Z0-9]+/i',
            'name' => '城通网盘',
            'icon' => '🏙️'
        ),
        // 115网盘
        array(
            'pattern' => '/https?:\/\/115\.com\/s\/[a-zA-Z0-9]+/i',
            'name' => '115网盘',
            'icon' => '💾'
        ),
        // 360云盘
        array(
            'pattern' => '/https?:\/\/(?:yunpan|share)\.360\.cn\/[a-zA-Z0-9]+/i',
            'name' => '360云盘',
            'icon' => '🔘'
        ),
        // 和彩云
        array(
            'pattern' => '/https?:\/\/caiyun\.139\.com\/[a-zA-Z0-9]+/i',
            'name' => '和彩云',
            'icon' => '☁️'
        )
    );
    
    foreach ($pan_types as $pan_type) {
        if (preg_match($pan_type['pattern'], $content_end, $matches)) {
            $result = array(
                'url' => $matches[0],
                'name' => $pan_type['name'],
                'icon' => $pan_type['icon']
            );
            wp_telegram_log('从文章底部提取到' . $pan_type['name'] . '链接: ' . $matches[0], 'debug');
            return $result;
        }
    }
    
    return false;
}

/**
 * 提取文章底部的网盘提取码
 * 
 * @param string $content 文章内容
 * @return string|bool 提取码或false
 */
function wp_telegram_extract_pan_code($content) {
    // 截取文章末尾部分（最后1000个字符）进行检查
    $content_end = mb_substr($content, -1000);
    
    // 匹配常见的提取码格式
    $patterns = array(
        // 匹配"提取码"或"密码"或"访问码"后面跟着的4个或更多字符
        '/(?:提取码|密码|访问码|提\s*取\s*码|验证码|授权码)[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配"密码"后面跟着的4个或更多字符
        '/(?:密\s*码)[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配"提码：xxxx"或"取码:xxxx"格式
        '/(?:提\s*码|取\s*码)[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配格式为"链接：xxx 提取码：xxxx"
        '/链接.*?提取码[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配"网盘密码"格式
        '/网盘密码[：:]\s*([a-zA-Z0-9]{4,})/i',
        // 匹配提取码后面有解压密码的情况
        '/提取码[：:]\s*([a-zA-Z0-9]{4,})[\s，,]*(?:解压|解压密码|解压码)/i',
        // 匹配带emoji的提取码格式
        '/[🔑📌🔐🔒][\s:：]*([a-zA-Z0-9]{4,})/i',
        // 匹配独立的提取码标记
        '/(?<![a-zA-Z0-9])([a-zA-Z0-9]{4})(?![a-zA-Z0-9]).*?(?:提取|密码|验证|授权)/i'
    );
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content_end, $matches)) {
            wp_telegram_log('从文章底部提取到提取码: ' . $matches[1], 'debug');
            return $matches[1];
        }
    }
    
    // 特殊情况：检查文章中是否包含形如"xxxx"的4位独立提取码，通常跟在网盘链接附近
    if (preg_match('/(?<![a-zA-Z0-9])([a-zA-Z0-9]{4})(?![a-zA-Z0-9])/i', $content_end, $matches)) {
        wp_telegram_log('从文章底部提取到可能的提取码: ' . $matches[1], 'debug');
        return $matches[1];
    }
    
    return false;
}

/**
 * 格式化网盘链接显示
 * 
 * @param array $pan_info 网盘信息
 * @param string $extract_code 提取码
 * @return string 格式化的网盘链接文本
 */
function wp_telegram_format_pan_link($pan_info, $extract_code = '') {
    $link_format = get_option('wp_telegram_integration_pan_link_format', 'button');
    
    if ($link_format === 'button' && $pan_info) {
        // 使用Telegram的按钮格式
        $text = "\n\n" . $pan_info['icon'] . " *" . $pan_info['name'] . "*";
        
        // 网盘链接直接显示为可点击的URL
        $text .= "\n" . $pan_info['url'];
        
        // 如果有提取码，单独显示
        if ($extract_code) {
            $text .= "\n📋 *提取码*: `" . $extract_code . "`";
        }
    } else {
        // 使用简洁文本格式
        $text = "\n\n" . $pan_info['icon'] . " *" . $pan_info['name'] . "* ";
        if ($extract_code) {
            $text .= "· 提取码: `" . $extract_code . "`";
        }
        $text .= "\n" . $pan_info['url'];
    }
    
    return $text;
}

/**
 * 根据设置决定是否处理网盘链接
 * 
 * @param string $message 原始消息
 * @param int $post_id 文章ID
 * @return string 处理后的消息
 */
function wp_telegram_handle_pan_link($message, $post_id) {
    $include_pan_link = get_option('wp_telegram_integration_include_pan_link', 'yes');
    
    if ($include_pan_link === 'yes') {
        $content = get_post_field('post_content', $post_id);
        $pan_info = wp_telegram_extract_pan_link($content);
        
        if ($pan_info) {
            // 提取提取码
            $extract_code = wp_telegram_extract_pan_code($content);
            
            // 格式化网盘链接
            $message .= wp_telegram_format_pan_link($pan_info, $extract_code);
            
            // 检查是否有解压密码
            $unzip_password = wp_telegram_extract_unzip_password($content);
            if ($unzip_password) {
                $message .= "\n🔓 *解压密码*: `" . $unzip_password . "`";
            }
            
            wp_telegram_log('添加了文章底部的网盘链接和提取码', 'debug');
        }
    }
    
    return $message;
}

/**
 * 提取解压密码
 * 
 * @param string $content 文章内容
 * @return string|bool 解压密码或false
 */
function wp_telegram_extract_unzip_password($content) {
    // 截取文章末尾部分（最后1000个字符）
    $content_end = mb_substr($content, -1000);
    
    // 匹配解压密码的格式
    $patterns = array(
        '/(?:解压密码|解压码|解压(?:缩)?(?:包)?密码)[：:]\s*([a-zA-Z0-9\.]{4,})/i',
        '/(?:压缩密码|压缩包密码)[：:]\s*([a-zA-Z0-9\.]{4,})/i',
        '/(?:文件密码)[：:]\s*([a-zA-Z0-9\.]{4,})/i'
    );
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content_end, $matches)) {
            wp_telegram_log('从文章底部提取到解压密码: ' . $matches[1], 'debug');
            return $matches[1];
        }
    }
    
    return false;
}

/**
 * 处理消息中的付费内容标记
 * 
 * @param string $message 原始消息文本
 * @param int $post_id 文章ID
 * @return string 处理后的消息
 */
function wp_telegram_process_paid_content($message, $post_id) {
    $pay_info = wp_telegram_is_paid_content($post_id);
    
    if ($pay_info) {
        $paid_text = wp_telegram_get_paid_content_text($pay_info);
        $message .= $paid_text;
        
        // 提取网盘链接
        $pan_info = wp_telegram_extract_pan_link(get_post_field('post_content', $post_id));
        if ($pan_info) {
            // 提取提取码
            $extract_code = wp_telegram_extract_pan_code(get_post_field('post_content', $post_id));
            
            // 格式化网盘链接
            $message .= wp_telegram_format_pan_link($pan_info, $extract_code);
            
            // 检查是否有解压密码
            $unzip_password = wp_telegram_extract_unzip_password(get_post_field('post_content', $post_id));
            if ($unzip_password) {
                $message .= "\n🔓 *解压密码*: `" . $unzip_password . "`";
            }
        }
    }
    
    return $message;
}

/**
 * 设置回调函数
 */
function wp_telegram_paid_content_settings_callback() {
    $include_pan_link = get_option('wp_telegram_integration_include_pan_link', 'yes');
    $pan_link_format = get_option('wp_telegram_integration_pan_link_format', 'button');
    ?>
    <h4>网盘链接设置</h4>
    <select name="wp_telegram_integration_include_pan_link">
        <option value="yes" <?php selected($include_pan_link, 'yes'); ?>>是 - 包含网盘链接和提取码</option>
        <option value="no" <?php selected($include_pan_link, 'no'); ?>>否 - 不包含网盘链接</option>
    </select>
    <p class="description">
        选择是否在文章推送时包含网盘链接和提取码。
    </p>
    
    <h4>网盘链接显示格式</h4>
    <select name="wp_telegram_integration_pan_link_format">
        <option value="button" <?php selected($pan_link_format, 'button'); ?>>标准格式 - 链接和提取码分行显示</option>
        <option value="text" <?php selected($pan_link_format, 'text'); ?>>紧凑格式 - 链接和提取码在同一行</option>
    </select>
    <p class="description">
        选择网盘链接在Telegram中的显示方式。
    </p>
    <?php
}

/**
 * 添加设置
 */
function wp_telegram_paid_content_settings() {
    add_settings_section(
        'wp_telegram_paid_content_section',
        '付费内容设置',
        function() {
            echo '<p>配置付费内容和网盘链接的处理方式</p>';
        },
        'wp-telegram-integration'
    );
    
    add_settings_field(
        'wp_telegram_integration_include_pan_link',
        '网盘链接设置',
        'wp_telegram_paid_content_settings_callback',
        'wp-telegram-integration',
        'wp_telegram_paid_content_section'
    );
    
    // 添加测试工具字段
    add_settings_field(
        'wp_telegram_integration_pan_link_test',
        '网盘链接测试工具',
        'wp_telegram_pan_link_test_callback',
        'wp-telegram-integration',
        'wp_telegram_paid_content_section'
    );
    
    register_setting('wp_telegram_integration_options', 'wp_telegram_integration_include_pan_link');
    register_setting('wp_telegram_integration_options', 'wp_telegram_integration_pan_link_format');
}
add_action('admin_init', 'wp_telegram_paid_content_settings');

/**
 * 网盘链接测试工具回调函数
 */
function wp_telegram_pan_link_test_callback() {
    ?>
    <div class="pan-link-test-container">
        <p class="description">在这里测试网盘链接提取功能。粘贴含有网盘链接和提取码的文本，点击测试按钮查看结果。</p>
        <textarea id="pan-link-test-content" style="width:100%; min-height:100px; margin-top:10px;" placeholder="在此处粘贴含有网盘链接和提取码的文本..."></textarea>
        <button id="pan-link-test-button" class="button button-primary" style="margin-top:10px;">测试网盘链接提取</button>
        
        <div id="pan-link-test-results" style="margin-top:15px; padding:10px; background:#f9f9f9; border:1px solid #ddd; display:none;">
            <h4>测试结果</h4>
            <div id="pan-link-result"></div>
            <div id="extract-code-result"></div>
            <div id="unzip-password-result"></div>
            <div id="formatted-result" style="margin-top:10px; padding:10px; background:#f5f5f5; border:1px solid #eee;"></div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#pan-link-test-button').click(function() {
                var content = $('#pan-link-test-content').val();
                if (!content) {
                    alert('请先输入测试内容');
                    return;
                }
                
                // AJAX请求进行测试
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wp_telegram_test_pan_link',
                        content: content,
                        nonce: '<?php echo wp_create_nonce('wp_telegram_test_pan_link'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            var data = response.data;
                            
                            // 显示结果区域
                            $('#pan-link-test-results').show();
                            
                            // 显示网盘链接
                            if (data.pan_info) {
                                $('#pan-link-result').html('<strong>网盘链接:</strong> ' + 
                                    data.pan_info.icon + ' ' + data.pan_info.name + '<br>' + 
                                    '<a href="' + data.pan_info.url + '" target="_blank">' + data.pan_info.url + '</a>');
                            } else {
                                $('#pan-link-result').html('<strong>网盘链接:</strong> <span style="color:red">未检测到网盘链接</span>');
                            }
                            
                            // 显示提取码
                            if (data.extract_code) {
                                $('#extract-code-result').html('<strong>提取码:</strong> <code>' + data.extract_code + '</code>');
                            } else {
                                $('#extract-code-result').html('<strong>提取码:</strong> <span style="color:red">未检测到提取码</span>');
                            }
                            
                            // 显示解压密码
                            if (data.unzip_password) {
                                $('#unzip-password-result').html('<strong>解压密码:</strong> <code>' + data.unzip_password + '</code>');
                            } else {
                                $('#unzip-password-result').html('<strong>解压密码:</strong> <span style="color:red">未检测到解压密码</span>');
                            }
                            
                            // 显示格式化结果
                            if (data.formatted_text) {
                                $('#formatted-result').html('<strong>格式化结果:</strong><pre>' + data.formatted_text.replace(/\n/g, '<br>') + '</pre>');
                            } else {
                                $('#formatted-result').html('');
                            }
                        } else {
                            alert('测试失败: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('请求失败，请稍后再试');
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}

/**
 * 处理网盘链接测试的AJAX请求
 */
function wp_telegram_test_pan_link_ajax() {
    // 安全检查
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_telegram_test_pan_link')) {
        wp_send_json_error('安全检查失败');
        return;
    }
    
    // 权限检查
    if (!current_user_can('manage_options')) {
        wp_send_json_error('权限不足');
        return;
    }
    
    $content = isset($_POST['content']) ? sanitize_textarea_field($_POST['content']) : '';
    if (empty($content)) {
        wp_send_json_error('内容不能为空');
        return;
    }
    
    // 提取网盘链接和提取码
    $pan_info = wp_telegram_extract_pan_link($content);
    $extract_code = wp_telegram_extract_pan_code($content);
    $unzip_password = wp_telegram_extract_unzip_password($content);
    
    // 生成格式化文本
    $formatted_text = '';
    if ($pan_info) {
        $formatted_text = wp_telegram_format_pan_link($pan_info, $extract_code);
        if ($unzip_password) {
            $formatted_text .= "\n🔓 *解压密码*: `" . $unzip_password . "`";
        }
    }
    
    // 返回结果
    wp_send_json_success(array(
        'pan_info' => $pan_info,
        'extract_code' => $extract_code,
        'unzip_password' => $unzip_password,
        'formatted_text' => $formatted_text
    ));
}
add_action('wp_ajax_wp_telegram_test_pan_link', 'wp_telegram_test_pan_link_ajax');

// 添加过滤器处理消息内容
function wp_telegram_filter_message_content($message, $post_id) {
    // 处理付费内容标记
    $message = wp_telegram_process_paid_content($message, $post_id);
    
    // 根据设置处理网盘链接
    // 只有在不是付费文章时才单独处理网盘链接
    // 因为付费文章的网盘链接已经在wp_telegram_process_paid_content中处理了
    if (!wp_telegram_is_paid_content($post_id)) {
        $message = wp_telegram_handle_pan_link($message, $post_id);
    }
    
    return $message;
}

// 将过滤器添加到核心模块
add_filter('wp_telegram_message_content', 'wp_telegram_filter_message_content', 10, 2); 