# Copyright (C) 2023 数据库批量替换
# This file is distributed under the same license as the 数据库批量替换 package.
msgid ""
msgstr ""
"Project-Id-Version: 数据库批量替换 1.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/sql-batch-"
"replace\n"
"POT-Creation-Date: 2023-10-17 04:15:43+00:00\n"
"PO-Revision-Date: 2023-10-17 12:28+0800\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.4\n"

#: func.php:46
msgid "请使用数据库批量替换插件修改网站地址，一键完美替换"
msgstr ""
"Please use the database batch replacement plugin to modify the "
"website address and replace it perfectly with one click"

#: func.php:46
msgid "立即修改"
msgstr "Modify Now"

#. Plugin Name of the plugin/theme
#: func.php:60
msgid "数据库批量替换"
msgstr "Database bulk replacement"

#: func.php:71 func.php:130
msgid "开始替换"
msgstr "Start Replace"

#: func.php:82
msgid "请确认将{old}替换为{new}"
msgstr "Please confirm to replace {old} with {new}"

#: func.php:83
msgid "共计替换{count}条数据，替换后无法撤回，请确保已备份数据库！"
msgstr ""
"A total of {count} pieces of data have been replaced, and cannot be "
"recalled after replacement. Please ensure that the database has been "
"backed up!"

#: func.php:84
msgid "替换明细：表 => 列 => 数量 || 序列化数量"
msgstr ""
"Replacement Details: Table=>Column=>Quantity | | Serialized Quantity"

#: func.php:85
msgid "重新查询"
msgstr "reopen an inquiry"

#: func.php:86
msgid "没有找到匹配的替换内容"
msgstr "No replacement matches found"

#: func.php:87
msgid "请再次确认替换【{old}】为【{new}】吗？"
msgstr "Can you please reconfirm replacing [{old}] with [{new}]?"

#: func.php:88
msgid "替换参数错误，请刷新后重试"
msgstr "Replacement parameter error, please refresh and try again"

#: func.php:89
msgid "正在批量替换中，请稍后..."
msgstr "Batch replacement in progress, please wait"

#: func.php:90
msgid "数据替换明细：表 => 列 => 数量"
msgstr "Data replacement details: Table=>Column=>Quantity"

#: func.php:91
msgid "序列化数据替换明细：表 => 列 => 数量"
msgstr "Serialized data replacement details: Table=>Column=>Quantity"

#: func.php:92
msgid "操作成功"
msgstr "Operation successful"

#: func.php:93
msgid "已将{old}替换为{new}"
msgstr "{old} has been replaced with {new}"

#: func.php:110
msgid "将"
msgstr "take"

#: func.php:114
msgid "替换为"
msgstr "Replace with"

#: func.php:118
msgid ""
"快速的批量替换数据库中的旧内容为新的内容，适用于网站换域名、加证书后"
"网址改为https、更换云储存后批量更换媒体链接等操作"
msgstr ""
"Quickly batch replace old content in the database with new content, "
"suitable for website domain name change, website address change to "
"https after certificate addition, and batch media link change after "
"cloud storage change"

#: func.php:119
msgid "操作仅需两步，全程自动化，快捷方便！"
msgstr ""
"The operation only requires two steps, and the entire process is "
"automated, fast and convenient!"

#: func.php:120
msgid "工具会自动分析数据库表，支持所有主题及插件！"
msgstr ""
"The tool will automatically analyze database tables, supporting all "
"themes and plugins!"

#: func.php:121
msgid ""
"自动判断序列化数据，序列化数据自动转义并替换，确保数据安全，真正意义"
"上解决SQL命令批量替换会出现的各种bug！"
msgstr ""
"Automatically determine serialized data, automatically escape and "
"replace serialized data, ensuring data security, and truly solving "
"various bugs that may occur during batch replacement of SQL commands!"

#: func.php:121 func.php:127
msgid "【了解相关原理】"
msgstr "[Understand relevant principles]"

#: func.php:123
msgid "注意事项："
msgstr "Precautions:"

#: func.php:124
msgid ""
"1.使用此工具前，请务必先备份数据库！！！！以避免操作错误带来的损失"
msgstr ""
"1. Before using this tool, please make sure to back up the database "
"first!!!! To avoid losses caused by operational errors"

#: func.php:125
msgid "2.替换时，请勿输入很短的替换值，容易误修改"
msgstr ""
"2. When replacing, do not enter very short replacement values, as "
"they may be modified by mistake"

#: func.php:126
msgid ""
"3.此工具仅批量修改数据库内容，对修改后会导致的结果无法判断，如果您不"
"清楚您的操作会带来什么结果，请谨慎操作"
msgstr ""
"3. This tool only modifies the database content in bulk, and it "
"cannot determine the results caused by the modifications. If you are "
"not sure what results your operation will bring, please operate with "
"caution"

#: func.php:127
msgid "4.此工具可无脑使用，但仍建议先了解数据库相关原理"
msgstr ""
"4. This tool can be used without thinking, but it is still "
"recommended to first understand the relevant principles of the "
"database"

#: func.php:131
msgid "下一步"
msgstr "Next step"

#: func.php:131
msgid "确认替换"
msgstr "Confirm replacement"

#: func.php:134
msgid "数据库批量替换小工具"
msgstr "Database Batch Replacement Widget"

#: func.php:134
msgid "查看官方教程"
msgstr "View official tutorials"

#: func.php:134
msgid ""
"zibll子比主题是一款功能强大、设计精美的资讯、商城、社区、论坛主题，如"
"果您还不知道，那就OUT了，点此了解详情"
msgstr ""
"Zibll Theme is a powerful and beautifully designed information, "
"mall, community, and forum theme. If you don’t know it yet, then "
"it’s OUT. Click here to learn more"

#: func.php:389 func.php:430
msgid "环境异常或登录状态已失效，请刷新页面重试"
msgstr ""
"The environment is abnormal or the login status has expired. Please "
"refresh the page and try again"

#: func.php:394 func.php:435
msgid "权限不足或登录状态已失效，请刷新页面重试"
msgstr ""
"Insufficient permissions or invalid login status. Please refresh the "
"page and try again"

#: func.php:401 func.php:442
msgid "请输入需要替换的旧内容"
msgstr "Please enter the old content that needs to be replaced"

#: func.php:405 func.php:446
msgid "请输入需要替换的新内容"
msgstr "Please enter the new content that needs to be replaced"

#: func.php:409 func.php:450
msgid "新内容不能与旧内容相同"
msgstr "New content cannot be the same as old content"

#: func.php:414
msgid "您输入的内容字符数量较少，替换时容易出现误修改情况，请认真确认"
msgstr ""
"The number of characters in the content you entered is relatively "
"small, and there may be errors when replacing it. Please confirm "
"carefully"

#: func.php:456
msgid "数据库替换完成，如您替换了网站域名，请自行跳转到新地址访问"
msgstr ""
"The database replacement is complete. If you have replaced the "
"website domain name, please redirect to the new address to access it "
"yourself"

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.zibll.com"
msgstr ""

#. Description of the plugin/theme
msgid ""
"数据一键批量替换小工具，适用于网站换域名、加证书后网址改为https、更换"
"云储存后批量更换媒体链接等操作"
msgstr ""
"One click batch data replacement tool, suitable for website domain "
"name change, website address change to https after certificate "
"addition, and batch media link change after cloud storage change"

#. Author of the plugin/theme
msgid "子比主题老唐-Qinver"
msgstr "zibll Theme:老唐-Qinver"
