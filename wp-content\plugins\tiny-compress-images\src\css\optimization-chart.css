div.tiny-optimization-chart {
	float: left;
	position: relative;
	width: 50%;
	text-align: center;
}

.ie8 div.tiny-optimization-chart {
	margin: 0;
}

div.tiny-optimization-chart svg circle.main {
	fill: #ebebeb;
	animation: shwoosh 2s ease;
	transition: stroke-dasharray 1s ease;
}

div.tiny-optimization-chart svg circle.inner {
	fill: #fff;
}

div.tiny-optimization-chart div.value {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	text-align: center;
}

div.tiny-optimization-chart div.value div.percentage {
	width: 50%;
	margin-left: auto;
	margin-right: auto;
	margin-top: 56px;
	font-size: 2.1em;
	line-height: 1em;
}

div.tiny-optimization-chart div.value div.label {
	width: 50%;
	margin-left: auto;
	margin-right: auto;
	font-size: 1.4em;
	line-height: 1.2em;
}

@media only screen and (max-width: 400px) {
	div.tiny-bulk-optimization div.tiny-optimization-chart {
		float: none;
		width: 100%;
	}
}
