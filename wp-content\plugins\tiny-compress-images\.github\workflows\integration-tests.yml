name: Integration Tests

on:
  push:
    branches:
      - '**'
  pull_request:
    branches:
      - '**'

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    strategy:
      matrix:
        include:
          - { wp: 46, php: 56 }
          - { wp: 46, php: 70 }
          - { wp: 57, php: 73 }
          - { wp: 60, php: 74 }
          - { wp: 63, php: 80 }
          - { wp: 67, php: 82 }
          - { wp: 68, php: 83 }
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Install dependencies
        run: npm ci

      - name: Get installed Playwright version
        id: playwright-version
        run: echo "PLAYWRIGHT_VERSION=$(node -p "require('./package.json').devDependencies['@playwright/test']")" >> $GITHUB_ENV

      - name: <PERSON><PERSON> Playwright
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: |
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}

      - run: npx playwright install --with-deps
        if: steps.playwright-cache.outputs.cache-hit != 'true'

      - name: Start WordPress
        uses: nick-fields/retry@v3
        with:
          timeout_minutes: 10
          max_attempts: 3
          shell: bash
          command: ./bin/run-wordpress ${{ matrix.wp }} ${{ matrix.php}}

      - name: Start Mocks
        run: ./bin/run-mocks 8100

      - name: Run integration tests
        run: npm run test:playwright
        env:
          WORDPRESS_PORT: 80${{ matrix.wp }}

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report-${{ matrix.wp }}-${{ matrix.php }}
          path: playwright-report/
          retention-days: 30
