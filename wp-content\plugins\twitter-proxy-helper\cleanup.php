<?php
/**
 * Twitter插件清理脚本
 * 清除缓存和重置错误状态
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// 检查权限
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Twitter插件清理工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .button { padding: 10px 20px; margin: 5px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; }
        .button:hover { background: #005a87; }
        .cleanup-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .cleanup-result.success { border-left-color: green; background: #f0fff0; }
        .cleanup-result.error { border-left-color: red; background: #fff0f0; }
    </style>
</head>
<body>
    <h1>🧹 Twitter插件清理工具</h1>
    <p>清理缓存和重置错误状态，确保修复生效</p>
    
    <?php
    if (isset($_POST['cleanup_action'])) {
        echo '<div class="section">';
        echo '<h2>清理结果</h2>';
        
        $action = $_POST['cleanup_action'];
        
        switch ($action) {
            case 'clear_cache':
                // 清除WordPress缓存
                if (function_exists('wp_cache_flush')) {
                    wp_cache_flush();
                    echo '<div class="cleanup-result success">✅ WordPress对象缓存已清除</div>';
                }
                
                // 清除插件缓存
                delete_transient('xyz_twap_cache');
                delete_transient('twitter_proxy_cache');
                echo '<div class="cleanup-result success">✅ 插件缓存已清除</div>';
                
                // 清除OPcache
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                    echo '<div class="cleanup-result success">✅ OPcache已清除</div>';
                } else {
                    echo '<div class="cleanup-result warning">⚠️ OPcache不可用</div>';
                }
                break;
                
            case 'reset_retry_counts':
                // 重置所有文章的重试计数
                global $wpdb;
                $deleted = $wpdb->delete(
                    $wpdb->postmeta,
                    array('meta_key' => '_twitter_proxy_retry_count'),
                    array('%s')
                );
                echo '<div class="cleanup-result success">✅ 已重置 ' . $deleted . ' 个文章的重试计数</div>';
                break;
                
            case 'clear_error_logs':
                // 清除错误日志
                $log_files = [
                    WP_CONTENT_DIR . '/debug.log',
                    WP_CONTENT_DIR . '/twitter-proxy-debug.log',
                    WP_CONTENT_DIR . '/twitter-token-fix.log'
                ];
                
                foreach ($log_files as $log_file) {
                    if (file_exists($log_file)) {
                        // 备份最后1000行
                        $lines = file($log_file);
                        if (count($lines) > 1000) {
                            $backup_lines = array_slice($lines, -1000);
                            file_put_contents($log_file, implode('', $backup_lines));
                            echo '<div class="cleanup-result success">✅ 已清理日志文件: ' . basename($log_file) . ' (保留最后1000行)</div>';
                        } else {
                            echo '<div class="cleanup-result success">✅ 日志文件 ' . basename($log_file) . ' 无需清理</div>';
                        }
                    }
                }
                break;
                
            case 'reset_auth_tokens':
                // 重置Twitter认证令牌
                delete_option('xyz_twap_tw_token');
                delete_option('xyz_twap_tw_refresh_token');
                delete_option('xyz_twap_last_auth_time');
                delete_option('xyz_twap_tw_af');
                echo '<div class="cleanup-result success">✅ Twitter认证令牌已重置</div>';
                echo '<div class="cleanup-result warning">⚠️ 需要重新进行Twitter授权</div>';
                break;
                
            case 'full_cleanup':
                // 执行完整清理
                
                // 1. 清除缓存
                if (function_exists('wp_cache_flush')) {
                    wp_cache_flush();
                }
                delete_transient('xyz_twap_cache');
                delete_transient('twitter_proxy_cache');
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                }
                echo '<div class="cleanup-result success">✅ 所有缓存已清除</div>';
                
                // 2. 重置重试计数
                global $wpdb;
                $deleted = $wpdb->delete(
                    $wpdb->postmeta,
                    array('meta_key' => '_twitter_proxy_retry_count'),
                    array('%s')
                );
                echo '<div class="cleanup-result success">✅ 已重置 ' . $deleted . ' 个重试计数</div>';
                
                // 3. 清理日志
                $log_files = [
                    WP_CONTENT_DIR . '/debug.log',
                    WP_CONTENT_DIR . '/twitter-proxy-debug.log',
                    WP_CONTENT_DIR . '/twitter-token-fix.log'
                ];
                
                foreach ($log_files as $log_file) {
                    if (file_exists($log_file)) {
                        $lines = file($log_file);
                        if (count($lines) > 1000) {
                            $backup_lines = array_slice($lines, -1000);
                            file_put_contents($log_file, implode('', $backup_lines));
                        }
                    }
                }
                echo '<div class="cleanup-result success">✅ 日志文件已清理</div>';
                
                // 4. 重新加载插件
                if (function_exists('twitter_proxy_apply_global_proxy')) {
                    twitter_proxy_apply_global_proxy();
                    echo '<div class="cleanup-result success">✅ 代理设置已重新加载</div>';
                }
                
                echo '<div class="cleanup-result success">🎉 完整清理完成！</div>';
                break;
        }
        
        echo '</div>';
    }
    ?>
    
    <div class="section">
        <h2>清理选项</h2>
        <form method="post">
            <p>
                <button type="submit" name="cleanup_action" value="clear_cache" class="button">
                    🗑️ 清除缓存
                </button>
                <small>清除WordPress和插件缓存</small>
            </p>
            
            <p>
                <button type="submit" name="cleanup_action" value="reset_retry_counts" class="button">
                    🔄 重置重试计数
                </button>
                <small>重置所有文章的Twitter发布重试计数</small>
            </p>
            
            <p>
                <button type="submit" name="cleanup_action" value="clear_error_logs" class="button">
                    📝 清理错误日志
                </button>
                <small>清理错误日志文件（保留最后1000行）</small>
            </p>
            
            <p>
                <button type="submit" name="cleanup_action" value="reset_auth_tokens" class="button">
                    🔑 重置认证令牌
                </button>
                <small>重置Twitter认证令牌（需要重新授权）</small>
            </p>
            
            <p>
                <button type="submit" name="cleanup_action" value="full_cleanup" class="button" 
                        onclick="return confirm('确定要执行完整清理吗？这将清除所有缓存和重置状态。')">
                    🧹 完整清理
                </button>
                <small>执行所有清理操作</small>
            </p>
        </form>
    </div>
    
    <div class="section">
        <h2>当前状态</h2>
        <?php
        // 显示当前状态
        echo '<p><strong>WordPress版本:</strong> ' . get_bloginfo('version') . '</p>';
        echo '<p><strong>PHP版本:</strong> ' . PHP_VERSION . '</p>';
        
        // 检查插件状态
        if (function_exists('twitter_proxy_get_proxy')) {
            echo '<p><strong>Twitter代理插件:</strong> <span class="success">✅ 已激活</span></p>';
        } else {
            echo '<p><strong>Twitter代理插件:</strong> <span class="error">❌ 未激活</span></p>';
        }
        
        if (class_exists('Abraham\TwitterOAuth\TwitterOAuth')) {
            echo '<p><strong>TwitterOAuth库:</strong> <span class="success">✅ 已加载</span></p>';
        } else {
            echo '<p><strong>TwitterOAuth库:</strong> <span class="error">❌ 未加载</span></p>';
        }
        
        // 检查重试计数
        global $wpdb;
        $retry_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = '_twitter_proxy_retry_count'");
        echo '<p><strong>待重试文章数:</strong> ' . $retry_count . '</p>';
        
        // 检查日志文件大小
        $log_files = [
            'debug.log' => WP_CONTENT_DIR . '/debug.log',
            'twitter-proxy-debug.log' => WP_CONTENT_DIR . '/twitter-proxy-debug.log',
            'twitter-token-fix.log' => WP_CONTENT_DIR . '/twitter-token-fix.log'
        ];
        
        echo '<p><strong>日志文件:</strong></p>';
        echo '<ul>';
        foreach ($log_files as $name => $path) {
            if (file_exists($path)) {
                $size = round(filesize($path) / 1024, 2);
                echo '<li>' . $name . ': ' . $size . ' KB</li>';
            } else {
                echo '<li>' . $name . ': 不存在</li>';
            }
        }
        echo '</ul>';
        ?>
    </div>
    
    <div class="section">
        <h2>快速链接</h2>
        <p>
            <a href="test-connection.php" class="button">🔧 连接测试</a>
            <a href="proxy-test.php" class="button">🌐 代理测试</a>
            <a href="test-publish.php" class="button">📤 发布测试</a>
        </p>
    </div>
    
    <p><strong>清理工具运行时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
</body>
</html>
