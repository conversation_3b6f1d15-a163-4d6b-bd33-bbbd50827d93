<?php
/**
 * Twitter 发布测试脚本
 * 
 * 用于测试Twitter发布功能
 */

// 加载WordPress环境
define('WP_USE_THEMES', false);

// 使用可靠的方法查找WordPress根目录
function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        if (file_exists($dir . '/wp-load.php')) {
            return $dir;
        }
    } while ($dir = realpath("$dir/.."));
    
    // 检查常见位置
    $possible_paths = [
        dirname(dirname(dirname(__DIR__))),
        dirname(dirname(__DIR__)),
        $_SERVER['DOCUMENT_ROOT'],
        '/var/www/html',
        '/mnt/sdc'
    ];
    
    foreach ($possible_paths as $path) {
        if (file_exists($path . '/wp-load.php')) {
            return $path;
        }
    }
    
    die('无法找到WordPress安装路径');
}

$wp_load_path = find_wordpress_base_path() . '/wp-load.php';
require_once($wp_load_path);

// 安全检查
if (!current_user_can('manage_options')) {
    die('权限不足');
}

// 设置页面头
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Twitter发布测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
        .section { margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
    </style>
</head>
<body>
    <h1>Twitter发布测试工具</h1>
    <p>此工具用于测试Twitter发布功能</p>
    
    <div class="section">
        <h2>Twitter设置检查</h2>
        <?php
        // 获取Twitter设置
        $client_id = get_option('xyz_twap_client_id');
        $client_secret = get_option('xyz_twap_client_secret');
        $token = get_option('xyz_twap_tw_token');
        $refresh_token = get_option('xyz_twap_tw_refresh_token');
        $last_auth = get_option('xyz_twap_last_auth_time');
        
        echo '<ul>';
        echo '<li>Client ID: ' . (empty($client_id) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span>') . '</li>';
        echo '<li>Client Secret: ' . (empty($client_secret) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span>') . '</li>';
        echo '<li>Access Token: ' . (empty($token) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span>') . '</li>';
        echo '<li>Refresh Token: ' . (empty($refresh_token) ? '<span class="error">未设置</span>' : '<span class="success">已设置</span>') . '</li>';
        echo '<li>上次授权时间: ' . (empty($last_auth) ? '<span class="error">未设置</span>' : '<span class="success">' . date('Y-m-d H:i:s', $last_auth) . '</span>') . '</li>';
        echo '</ul>';
        ?>
    </div>
    
    <div class="section">
        <h2>代理设置</h2>
        <?php
        // 检查代理设置
        $proxy = function_exists('twitter_proxy_get_proxy') ? twitter_proxy_get_proxy() : '未找到twitter_proxy_get_proxy函数';
        echo '<p>当前代理设置: <strong>' . esc_html($proxy) . '</strong></p>';
        
        // 测试代理连接
        $ch = curl_init('https://api.twitter.com/2/openapi.json');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        // 应用代理
        if (strpos($proxy, 'socks5') === 0) {
            $proxy_address = str_replace(['socks5://', 'socks5h://'], '', $proxy);
            $parts = explode(':', $proxy_address);
            if (count($parts) === 2) {
                curl_setopt($ch, CURLOPT_PROXY, $parts[0]);
                curl_setopt($ch, CURLOPT_PROXYPORT, $parts[1]);
                curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // SOCKS5
            }
        }
        
        $start = microtime(true);
        $result = curl_exec($ch);
        $time = round((microtime(true) - $start) * 1000);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($error) {
            echo '<p class="error">代理测试: 失败 - ' . $error . '</p>';
        } else {
            echo '<p class="success">代理测试: 成功 - HTTP状态码: ' . $httpCode . ' (响应时间: ' . $time . 'ms)</p>';
        }
        ?>
    </div>
    
    <div class="section">
        <h2>发送测试推文</h2>
        <?php
        if (isset($_POST['send_test_tweet'])) {
            $message = isset($_POST['tweet_message']) ? $_POST['tweet_message'] : '这是一条测试推文 ' . date('Y-m-d H:i:s');
            
            if (function_exists('xyz_twap_publish_to_twitter')) {
                echo '<h3>正在发送推文...</h3>';
                echo '<p>消息内容: ' . esc_html($message) . '</p>';
                
                try {
                    // 将环境变量设置为使用代理
                    if (!empty($proxy) && strpos($proxy, 'socks5') === 0) {
                        $proxy_address = str_replace(['socks5://', 'socks5h://'], '', $proxy);
                        putenv('http_proxy=socks5://' . $proxy_address);
                        putenv('https_proxy=socks5://' . $proxy_address);
                        echo '<p>已设置环境变量代理: socks5://' . $proxy_address . '</p>';
                    }
                    
                    // 记录开始时间
                    $start_time = microtime(true);
                    
                    // 发送推文
                    $result = xyz_twap_publish_to_twitter($message, '', 0);
                    
                    $time_taken = round((microtime(true) - $start_time) * 1000);
                    
                    if ($result === true) {
                        echo '<p class="success">发送成功! (耗时: ' . $time_taken . 'ms)</p>';
                    } else {
                        echo '<p class="error">发送失败: ' . esc_html($result) . ' (耗时: ' . $time_taken . 'ms)</p>';
                    }
                } catch (Exception $e) {
                    echo '<p class="error">发送时发生异常: ' . esc_html($e->getMessage()) . '</p>';
                }
                
                // 清除环境变量
                putenv('http_proxy');
                putenv('https_proxy');
            } else {
                echo '<p class="error">找不到Twitter发布函数 (xyz_twap_publish_to_twitter)</p>';
            }
        }
        ?>
        <form method="post">
            <p><label for="tweet_message">推文内容:</label></p>
            <p><textarea name="tweet_message" id="tweet_message" rows="3" style="width: 100%;"><?php echo '这是一条测试推文 ' . date('Y-m-d H:i:s'); ?></textarea></p>
            <p><button type="submit" name="send_test_tweet" class="button button-primary">发送测试推文</button></p>
            <p><small>警告：这将实际发送一条推文到您的Twitter账号</small></p>
        </form>
    </div>
    
    <div class="section">
        <h2>日志查看</h2>
        <?php
        $log_files = [
            '代理日志' => WP_CONTENT_DIR . '/twitter-proxy-debug.log',
            '授权回调日志' => WP_CONTENT_DIR . '/twitter-auth-callback.log',
            'WordPress调试日志' => WP_CONTENT_DIR . '/debug.log'
        ];
        
        foreach ($log_files as $name => $path) {
            echo "<h3>$name</h3>";
            if (file_exists($path)) {
                $size = filesize($path);
                $human_size = round($size / 1024, 2) . ' KB';
                echo "<p>文件大小: $human_size</p>";
                
                // 显示最后15行
                $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                if ($lines) {
                    $last_lines = array_slice($lines, -15);
                    echo '<h4>最后15行日志:</h4>';
                    echo '<pre>' . implode("\n", $last_lines) . '</pre>';
                }
            } else {
                echo "<p class='error'>日志文件不存在</p>";
            }
        }
        ?>
    </div>
</body>
</html> 