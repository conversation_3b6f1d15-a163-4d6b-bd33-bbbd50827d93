#!/bin/bash

# AWS韩国服务器SOCKS5代理优化部署脚本
# 适用于Ubuntu 20.04/22.04 LTS
# 作者: Twitter代理助手
# 版本: 2.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统环境..."
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    source /etc/os-release
    
    if [[ "$ID" != "ubuntu" ]]; then
        log_warn "此脚本专为Ubuntu设计，当前系统: $ID"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_info "系统: $PRETTY_NAME"
    log_info "内核: $(uname -r)"
    log_info "架构: $(uname -m)"
}

# 更新系统
update_system() {
    log_step "更新系统包..."
    
    apt update -y
    apt upgrade -y
    
    # 安装必要工具
    apt install -y curl wget net-tools htop iftop iotop vim ufw fail2ban
    
    log_info "系统更新完成"
}

# 安装和配置3proxy
install_3proxy() {
    log_step "安装3proxy SOCKS5代理服务..."
    
    # 安装3proxy
    apt install -y 3proxy
    
    # 创建配置目录
    mkdir -p /etc/3proxy
    mkdir -p /var/log/3proxy
    
    # 生成优化的配置文件
    cat > /etc/3proxy/3proxy.cfg << 'EOF'
# 3proxy配置文件 - AWS韩国服务器优化版
# 针对Twitter API访问优化

# 守护进程模式
daemon
pidfile /var/run/3proxy.pid

# DNS设置 - 使用Google和Cloudflare DNS
nserver 8.8.8.8
nserver 1.1.1.1
nserver 8.8.4.4
nserver 1.0.0.1
nscache 65536
nscache6 65536

# 超时设置 - 针对跨国网络优化
# 格式: resolve, connect, read, write, tcp_close, udp_idle, dns_timeout, tcp_timeout
timeouts 10 30 60 60 300 1800 30 120

# 日志配置
log /var/log/3proxy/3proxy.log D
logformat "- +_L%t.%. %N.%p %E %U %C:%c %R:%r %O %I %h %T"
rotate 30

# 计数器配置
counter /var/log/3proxy/3proxy.3cf
rotate 30

# 访问控制 - 只允许特定端口和协议
auth none

# 允许HTTP/HTTPS连接
allow * * * 80,8080,8000-8999 HTTP
allow * * * 443,8443 HTTPS

# 允许CONNECT方法（用于HTTPS隧道）
allow * * * 1-65535 CONNECT

# 拒绝其他连接
deny *

# SOCKS5代理配置
# 监听所有接口的7890端口
socks -i0.0.0.0 -p7890

# 可选：HTTP代理（如果需要）
# proxy -i0.0.0.0 -p8080

# 性能优化设置
maxconn 1000
EOF

    log_info "3proxy配置文件已创建"
}

# 系统网络优化
optimize_network() {
    log_step "优化系统网络参数..."
    
    # 备份原始配置
    cp /etc/sysctl.conf /etc/sysctl.conf.backup
    
    # 添加网络优化参数
    cat >> /etc/sysctl.conf << 'EOF'

# Twitter代理服务器网络优化配置
# 针对AWS韩国服务器和跨国网络传输优化

# TCP缓冲区优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.rmem_default = 65536
net.core.wmem_default = 65536
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# TCP连接优化
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_no_metrics_save = 1

# 连接跟踪优化
net.netfilter.nf_conntrack_max = 1048576
net.netfilter.nf_conntrack_tcp_timeout_established = 7200

# 文件描述符限制
fs.file-max = 1048576

# 网络队列优化
net.core.netdev_max_backlog = 5000
net.core.netdev_budget = 600

# TCP快速打开
net.ipv4.tcp_fastopen = 3

# 减少TIME_WAIT状态的连接数量
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_tw_reuse = 1

# IP转发（如果需要）
# net.ipv4.ip_forward = 1
EOF

    # 应用配置
    sysctl -p
    
    log_info "网络参数优化完成"
}

# 配置防火墙
setup_firewall() {
    log_step "配置防火墙..."
    
    # 重置ufw
    ufw --force reset
    
    # 默认策略
    ufw default deny incoming
    ufw default allow outgoing
    
    # 允许SSH
    ufw allow ssh
    
    # 允许SOCKS5代理端口（限制来源IP）
    read -p "请输入允许访问代理的客户端服务器IP地址: " CLIENT_IP
    
    if [[ -n "$CLIENT_IP" ]]; then
        ufw allow from $CLIENT_IP to any port 7890 comment 'SOCKS5 Proxy'
        log_info "已允许 $CLIENT_IP 访问SOCKS5代理"
    else
        log_warn "未设置客户端IP限制，允许所有IP访问（不推荐）"
        ufw allow 7890 comment 'SOCKS5 Proxy - All IPs'
    fi
    
    # 启用防火墙
    ufw --force enable
    
    log_info "防火墙配置完成"
}

# 配置fail2ban
setup_fail2ban() {
    log_step "配置fail2ban防护..."
    
    # 创建3proxy的fail2ban配置
    cat > /etc/fail2ban/filter.d/3proxy.conf << 'EOF'
[Definition]
failregex = ^.*\s+<HOST>\s+.*\s+denied\s+.*$
ignoreregex =
EOF

    cat > /etc/fail2ban/jail.d/3proxy.conf << 'EOF'
[3proxy]
enabled = true
port = 7890
filter = 3proxy
logpath = /var/log/3proxy/3proxy.log
maxretry = 5
bantime = 3600
findtime = 600
EOF

    # 重启fail2ban
    systemctl restart fail2ban
    
    log_info "fail2ban配置完成"
}

# 创建systemd服务
create_service() {
    log_step "创建systemd服务..."
    
    cat > /etc/systemd/system/3proxy.service << 'EOF'
[Unit]
Description=3proxy Proxy Server
After=network.target

[Service]
Type=forking
PIDFile=/var/run/3proxy.pid
ExecStart=/usr/bin/3proxy /etc/3proxy/3proxy.cfg
ExecReload=/bin/kill -HUP $MAINPID
KillMode=process
Restart=on-failure
RestartSec=5s

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/3proxy /var/run

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用并启动服务
    systemctl enable 3proxy
    systemctl start 3proxy
    
    log_info "3proxy服务已启动"
}

# 性能监控脚本
create_monitor_script() {
    log_step "创建监控脚本..."
    
    cat > /usr/local/bin/proxy-monitor.sh << 'EOF'
#!/bin/bash

# 3proxy监控脚本

LOG_FILE="/var/log/proxy-monitor.log"
PROXY_PORT=7890

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查端口是否监听
check_port() {
    if netstat -tuln | grep -q ":$PROXY_PORT "; then
        return 0
    else
        return 1
    fi
}

# 检查进程是否运行
check_process() {
    if pgrep -f "3proxy" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# 主检查逻辑
main() {
    if ! check_port || ! check_process; then
        log_message "ERROR: 3proxy服务异常，尝试重启..."
        systemctl restart 3proxy
        sleep 5
        
        if check_port && check_process; then
            log_message "INFO: 3proxy服务重启成功"
        else
            log_message "ERROR: 3proxy服务重启失败"
        fi
    else
        log_message "INFO: 3proxy服务运行正常"
    fi
}

main
EOF

    chmod +x /usr/local/bin/proxy-monitor.sh
    
    # 添加到crontab
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/proxy-monitor.sh") | crontab -
    
    log_info "监控脚本已创建并添加到定时任务"
}

# 显示配置信息
show_info() {
    log_step "部署完成！配置信息如下："
    
    echo
    echo "=================================="
    echo "AWS韩国SOCKS5代理服务器信息"
    echo "=================================="
    echo "服务器IP: $(curl -s ifconfig.me 2>/dev/null || echo "获取失败")"
    echo "SOCKS5端口: 7890"
    echo "代理URL: socks5://$(curl -s ifconfig.me 2>/dev/null):7890"
    echo "服务状态: $(systemctl is-active 3proxy)"
    echo "=================================="
    echo
    
    echo "管理命令:"
    echo "  启动服务: systemctl start 3proxy"
    echo "  停止服务: systemctl stop 3proxy"
    echo "  重启服务: systemctl restart 3proxy"
    echo "  查看状态: systemctl status 3proxy"
    echo "  查看日志: tail -f /var/log/3proxy/3proxy.log"
    echo "  监控脚本: /usr/local/bin/proxy-monitor.sh"
    echo
    
    echo "测试命令:"
    echo "  curl --proxy socks5://$(curl -s ifconfig.me 2>/dev/null):7890 https://api.twitter.com/2/openapi.json"
    echo
}

# 主函数
main() {
    log_info "开始部署AWS韩国SOCKS5代理服务器..."
    
    check_root
    check_system
    update_system
    install_3proxy
    optimize_network
    setup_firewall
    setup_fail2ban
    create_service
    create_monitor_script
    show_info
    
    log_info "部署完成！"
}

# 执行主函数
main "$@"
