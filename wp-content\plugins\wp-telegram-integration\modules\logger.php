<?php
/**
 * 日志系统模块
 * 
 * 提供日志记录和查看功能
 */

// 禁止直接访问
if (!defined('WPINC')) {
    die;
}

/**
 * 记录日志
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别 (debug, info, warning, error, critical)
 * @return bool 是否成功记录
 */
function wp_telegram_log($message, $level = 'info') {
    // 日志级别定义
    $log_levels = array(
        'debug' => 0,    // 最低级别，调试信息
        'info' => 1,     // 普通信息
        'warning' => 2,  // 警告信息
        'error' => 3,    // 错误信息
        'critical' => 4  // 严重错误
    );
    
    // 获取当前设置的记录级别
    $current_level = get_option('wp_telegram_integration_log_level', 'error');
    
    // 如果日志级别小于设置的级别，不记录
    if ($log_levels[$level] < $log_levels[$current_level]) {
        return false;
    }
    
    // 格式化日志消息
    $formatted_message = '[' . date('Y-m-d H:i:s') . '] [' . strtoupper($level) . '] [TelegramIntegration] ' . $message;
    
    // 记录到WordPress日志
    error_log($formatted_message);
    
    return true;
}

/**
 * 添加日志页面
 */
function wp_telegram_log_viewer_menu() {
    add_submenu_page(
        'options-general.php',
        'Telegram集成日志',
        'Telegram日志',
        'manage_options',
        'wp-telegram-log-viewer',
        'wp_telegram_log_viewer_page'
    );
}
add_action('admin_menu', 'wp_telegram_log_viewer_menu');

/**
 * 日志查看页面
 */
function wp_telegram_log_viewer_page() {
    // 确认用户权限
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 处理日志清除请求
    if (isset($_POST['wp_telegram_clear_log']) && check_admin_referer('wp_telegram_clear_log', 'wp_telegram_clear_log_nonce')) {
        wp_telegram_clear_log();
        echo '<div class="notice notice-success is-dismissible"><p>日志已清除。</p></div>';
    }
    
    // 获取日志文件路径
    $log_file = WP_CONTENT_DIR . '/debug.log';
    
    // 检查日志文件是否存在和可读
    if (!file_exists($log_file) || !is_readable($log_file)) {
        echo '<div class="wrap"><h1>Telegram集成日志查看器</h1>';
        echo '<div class="notice notice-error"><p>无法读取日志文件。请确保日志文件存在并可读取：' . esc_html($log_file) . '</p></div>';
        echo '</div>';
        return;
    }
    
    // 获取当前日志级别设置
    $current_level = get_option('wp_telegram_integration_log_level', 'error');
    
    // 页面标题和介绍
    echo '<div class="wrap">';
    echo '<h1>Telegram集成日志查看器</h1>';
    echo '<p>当前日志级别设置: <strong>' . esc_html(strtoupper($current_level)) . '</strong></p>';
    
    // 添加清除日志按钮
    echo '<form method="post" action="">';
    wp_nonce_field('wp_telegram_clear_log', 'wp_telegram_clear_log_nonce');
    echo '<input type="hidden" name="wp_telegram_clear_log" value="1">';
    submit_button('清除日志', 'delete', 'clear_log', false, array('onclick' => 'return confirm("确定要清除所有日志吗？此操作不可撤销。");'));
    echo '</form>';
    
    // 读取日志内容
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    
    // 过滤只显示Telegram集成相关的日志
    $telegram_logs = array();
    foreach ($log_lines as $line) {
        if (strpos($line, '[TelegramIntegration]') !== false || 
            strpos($line, '[TelegramSync]') !== false || 
            strpos($line, '[TelegramSync-ImageFix]') !== false || 
            strpos($line, '[TelegramSync-Fixed]') !== false || 
            strpos($line, '[TelegramSync-Proxy]') !== false) {
            $telegram_logs[] = $line;
        }
    }
    
    // 反转数组显示最新的日志在前面
    $telegram_logs = array_reverse($telegram_logs);
    
    // 限制显示最近的500条日志
    $telegram_logs = array_slice($telegram_logs, 0, 500);
    
    // 显示日志内容
    if (count($telegram_logs) > 0) {
        echo '<div class="card" style="margin-top: 20px;">';
        echo '<h2>最近的日志记录</h2>';
        echo '<div style="background-color: #f5f5f5; padding: 10px; max-height: 500px; overflow-y: auto;">';
        echo '<pre style="margin: 0; white-space: pre-wrap;">';
        foreach ($telegram_logs as $log) {
            // 根据日志级别添加颜色
            if (strpos($log, '[ERROR]') !== false || strpos($log, '[CRITICAL]') !== false) {
                echo '<span style="color: #d63638;">' . esc_html($log) . '</span>' . "\n";
            } elseif (strpos($log, '[WARNING]') !== false) {
                echo '<span style="color: #dba617;">' . esc_html($log) . '</span>' . "\n";
            } elseif (strpos($log, '[INFO]') !== false) {
                echo '<span style="color: #2271b1;">' . esc_html($log) . '</span>' . "\n";
            } elseif (strpos($log, '[DEBUG]') !== false) {
                echo '<span style="color: #646970;">' . esc_html($log) . '</span>' . "\n";
            } else {
                echo esc_html($log) . "\n";
            }
        }
        echo '</pre>';
        echo '</div>';
        echo '</div>';
    } else {
        echo '<div class="notice notice-info"><p>没有找到Telegram集成相关的日志记录。</p></div>';
    }
    
    echo '</div>';
}

/**
 * 清除日志文件
 */
function wp_telegram_clear_log() {
    $log_file = WP_CONTENT_DIR . '/debug.log';
    
    // 备份原日志
    if (file_exists($log_file)) {
        $backup_file = WP_CONTENT_DIR . '/debug_backup_' . time() . '.log';
        copy($log_file, $backup_file);
        
        // 清空日志文件
        file_put_contents($log_file, '');
        
        // 记录日志已被清除
        wp_telegram_log('日志已被管理员清除并备份', 'info');
        
        return true;
    }
    
    return false;
} 