# Copyright (C) 2024 Till <PERSON><PERSON><PERSON><PERSON>
# This file is distributed under the GPLv3.
msgid ""
msgstr ""
"Project-Id-Version: Redis Object Cache 2.5.4\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/redis-cache\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-10-15T17:40:24+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.11.0\n"
"X-Domain: redis-cache\n"

#. Plugin Name of the plugin
#: redis-cache.php
#: includes/class-plugin.php:149
#: includes/class-plugin.php:221
#: includes/ui/settings.php:19
msgid "Redis Object Cache"
msgstr ""

#. Plugin URI of the plugin
#: redis-cache.php
msgid "https://wordpress.org/plugins/redis-cache/"
msgstr ""

#. Description of the plugin
#: redis-cache.php
msgid "A persistent object cache backend powered by Redis. Supports Predis, PhpRedis, Relay, replication, sentinels, clustering and WP-CLI."
msgstr ""

#. Author of the plugin
#: redis-cache.php
msgid "Till Krüss"
msgstr ""

#. Author URI of the plugin
#: redis-cache.php
msgid "https://objectcache.pro"
msgstr ""

#: includes/class-plugin.php:150
msgid "Redis"
msgstr ""

#: includes/class-plugin.php:186
msgid "Overview"
msgstr ""

#: includes/class-plugin.php:192
msgid "Metrics"
msgstr ""

#: includes/class-plugin.php:198
msgid "Diagnostics"
msgstr ""

#: includes/class-plugin.php:249
#: includes/class-plugin.php:769
#: includes/ui/widget.php:38
msgid "Settings"
msgstr ""

#: includes/class-plugin.php:289
msgctxt "verb"
msgid "Upgrade to Pro"
msgstr ""

#: includes/class-plugin.php:388
#: includes/ui/tabs/metrics.php:17
#: includes/ui/widget.php:18
msgid "Time"
msgstr ""

#: includes/class-plugin.php:389
#: includes/ui/tabs/metrics.php:22
#: includes/ui/widget.php:23
msgid "Bytes"
msgstr ""

#: includes/class-plugin.php:390
#: includes/ui/tabs/metrics.php:27
#: includes/ui/widget.php:28
msgid "Ratio"
msgstr ""

#: includes/class-plugin.php:391
#: includes/ui/tabs/metrics.php:32
#: includes/ui/widget.php:33
msgid "Calls"
msgstr ""

#: includes/class-plugin.php:392
msgid "Not enough data collected, yet."
msgstr ""

#: includes/class-plugin.php:393
msgid "Enable object cache to collect data."
msgstr ""

#: includes/class-plugin.php:539
#: includes/class-qm-collector.php:77
#: includes/ui/tabs/overview.php:59
msgid "Disabled"
msgstr ""

#: includes/class-plugin.php:543
msgid "Not enabled"
msgstr ""

#: includes/class-plugin.php:547
msgid "Drop-in is outdated"
msgstr ""

#: includes/class-plugin.php:551
msgid "Drop-in is invalid"
msgstr ""

#: includes/class-plugin.php:556
msgid "Connected"
msgstr ""

#: includes/class-plugin.php:557
msgid "Not connected"
msgstr ""

#: includes/class-plugin.php:560
#: includes/ui/tabs/overview.php:266
msgid "Unknown"
msgstr ""

#. translators: %s = Action link to update the drop-in.
#: includes/class-plugin.php:706
msgid "The Redis object cache drop-in is outdated. Please <a href=\"%s\">update the drop-in</a>."
msgstr ""

#. translators: %s = Link to settings page.
#: includes/class-plugin.php:713
msgid "A foreign object cache drop-in was found. To use Redis for object caching, please <a href=\"%s\">enable the drop-in</a>."
msgstr ""

#: includes/class-plugin.php:740
#: includes/class-qm-collector.php:34
#: includes/class-qm-output.php:37
msgid "Object Cache"
msgstr ""

#: includes/class-plugin.php:761
#: includes/ui/tabs/overview.php:278
msgid "Flush Cache"
msgstr ""

#. translators: %s = The status of the Redis connection.
#: includes/class-plugin.php:785
msgid "Status: %s"
msgstr ""

#. translators: 1: Hit ratio, 2: Hits, 3: Misses. 4: Human-readable size of cache.
#: includes/class-plugin.php:805
msgid "(Current page) Hit Ratio: %1$s%%, Hits %2$s, Misses: %3$s, Size: %4$s"
msgstr ""

#: includes/class-plugin.php:852
msgid "Flushing cache..."
msgstr ""

#: includes/class-plugin.php:928
msgid "Object cache flushed."
msgstr ""

#: includes/class-plugin.php:934
msgid "Object cache could not be flushed."
msgstr ""

#: includes/class-plugin.php:966
#: includes/cli/class-commands.php:80
msgid "Object cache enabled."
msgstr ""

#: includes/class-plugin.php:972
#: includes/cli/class-commands.php:82
msgid "Object cache could not be enabled."
msgstr ""

#: includes/class-plugin.php:996
#: includes/cli/class-commands.php:121
msgid "Object cache disabled."
msgstr ""

#: includes/class-plugin.php:1002
#: includes/cli/class-commands.php:123
msgid "Object cache could not be disabled."
msgstr ""

#: includes/class-plugin.php:1027
#: includes/cli/class-commands.php:162
msgid "Updated object cache drop-in and enabled Redis object cache."
msgstr ""

#: includes/class-plugin.php:1033
#: includes/cli/class-commands.php:164
msgid "Object cache drop-in could not be updated."
msgstr ""

#. translators: %s = Action link to update the drop-in.
#: includes/class-plugin.php:1108
msgid "The Object Cache Pro plugin appears to be installed and should be used. You can safely <a href=\"%s\">uninstall Redis Object Cache</a>."
msgstr ""

#: includes/class-plugin.php:1140
msgid "Object Cache Pro!"
msgstr ""

#. translators: %s = Link to the plugin setting screen.
#: includes/class-plugin.php:1143
msgid "A <u>business class</u> object cache backend. Truly reliable, highly-optimized and fully customizable, with a <u>dedicated engineer</u> when you most need it. <a href=\"%s\">Learn more »</a>"
msgstr ""

#: includes/class-plugin.php:1188
msgid "Object Cache Pro + WooCommerce = ❤️"
msgstr ""

#. translators: %s = Link to the plugin's settings screen.
#: includes/class-plugin.php:1191
msgid "Object Cache Pro is a <u>business class</u> object cache that’s highly-optimized for WooCommerce to provide true reliability, peace of mind and faster load times for your store. <a style=\"color: #bb77ae;\" href=\"%s\">Learn more »</a>"
msgstr ""

#. translators: %1$d = number of objects. %2$s = human-readable size of cache. %3$s = name of the used client.
#: includes/class-plugin.php:1261
msgid "Retrieved %1$d objects (%2$s) from Redis using %3$s."
msgstr ""

#: includes/class-plugin.php:1376
msgid "File modifications are not allowed."
msgstr ""

#: includes/class-plugin.php:1380
msgid "Could not initialize filesystem."
msgstr ""

#: includes/class-plugin.php:1391
msgid "Object cache drop-in is not writable."
msgstr ""

#: includes/class-plugin.php:1401
msgid "Object cache file doesn’t exist."
msgstr ""

#: includes/class-plugin.php:1406
msgid "Test file exists, but couldn’t be deleted."
msgstr ""

#: includes/class-plugin.php:1411
msgid "Content directory is not writable."
msgstr ""

#: includes/class-plugin.php:1415
msgid "Failed to copy test file."
msgstr ""

#: includes/class-plugin.php:1419
msgid "Copied test file doesn’t exist."
msgstr ""

#: includes/class-plugin.php:1425
msgid "Couldn’t verify test file contents."
msgstr ""

#: includes/class-plugin.php:1429
msgid "Copied test file couldn’t be deleted."
msgstr ""

#: includes/class-qm-collector.php:77
msgid "Yes"
msgstr ""

#: includes/class-qm-output.php:102
msgid "The Redis Object Cache drop-in is not installed. Use WP CLI or go to \"Settings -> Redis\" to enable drop-in."
msgstr ""

#: includes/class-qm-output.php:113
msgid "WordPress is using a foreign object cache drop-in and Redis Object Cache is not being used. Use WP CLI or go to \"Settings -> Redis\" to enable drop-in."
msgstr ""

#: includes/cli/class-commands.php:58
msgid "Redis object cache already enabled."
msgstr ""

#: includes/cli/class-commands.php:60
#: includes/cli/class-commands.php:112
msgid "A foreign object cache drop-in was found. To use Redis for object caching, run: `wp redis update-dropin`."
msgstr ""

#. translators: %s = The Redis connection error message.
#: includes/cli/class-commands.php:67
msgid "Object cache could not be enabled. Redis server is unreachable: %s"
msgstr ""

#: includes/cli/class-commands.php:106
msgid "No object cache drop-in found."
msgstr ""

#. translators: %s = The Redis connection error message.
#: includes/cli/class-commands.php:159
msgid "Object cache drop-in could not be updated. Redis server is unreachable: %s"
msgstr ""

#: includes/object-cache.php:2993
msgid "Error establishing a Redis connection"
msgstr ""

#. translators: %s = Formatted wp-config.php file name.
#: includes/object-cache.php:3000
msgid "WordPress is unable to establish a connection to Redis. This means that the connection information in your %s file are incorrect, or that the Redis server is not reachable."
msgstr ""

#: includes/object-cache.php:3005
msgid "Is the correct Redis host and port set?"
msgstr ""

#: includes/object-cache.php:3006
msgid "Is the Redis server running?"
msgstr ""

#. translators: %s = Link to installation instructions.
#: includes/object-cache.php:3011
msgid "If you need help, please read the <a href=\"%s\">installation instructions</a>."
msgstr ""

#. translators: %1$s = Formatted object-cache.php file name, %2$s = Formatted wp-content directory name.
#: includes/object-cache.php:3018
msgid "To disable Redis, delete the %1$s file in the %2$s directory."
msgstr ""

#. translators: %s = Tab label.
#: includes/ui/class-tab.php:154
msgid "%s are disabled for this site."
msgstr ""

#: includes/ui/query-monitor.php:20
msgid "Status"
msgstr ""

#: includes/ui/query-monitor.php:25
msgid "Hit Ratio"
msgstr ""

#: includes/ui/query-monitor.php:30
msgid "Hits"
msgstr ""

#: includes/ui/query-monitor.php:35
msgid "Misses"
msgstr ""

#: includes/ui/query-monitor.php:40
msgid "Size"
msgstr ""

#: includes/ui/query-monitor.php:50
msgid "Errors"
msgstr ""

#: includes/ui/query-monitor.php:73
msgid "Global Groups"
msgstr ""

#: includes/ui/query-monitor.php:87
msgid "Non-persistent Groups"
msgstr ""

#: includes/ui/query-monitor.php:101
msgid "Unflushable Groups"
msgstr ""

#: includes/ui/query-monitor.php:115
msgid "Metadata"
msgstr ""

#: includes/ui/settings.php:71
msgid "Resources"
msgstr ""

#: includes/ui/settings.php:79
msgid "Need more performance and reliability?"
msgstr ""

#. translators: %s = Object Cache Pro.
#: includes/ui/settings.php:83
msgid "Check out %s"
msgstr ""

#: includes/ui/settings.php:87
msgid "<strong>A business class object cache backend.</strong> Truly reliable, highly-optimized and fully customizable, with a <u>dedicated engineer</u> when you most need it."
msgstr ""

#: includes/ui/settings.php:90
msgid "Rewritten for raw performance"
msgstr ""

#: includes/ui/settings.php:91
msgid "100% WordPress API compliant"
msgstr ""

#: includes/ui/settings.php:92
msgid "Faster serialization and compression"
msgstr ""

#: includes/ui/settings.php:93
msgid "Easy debugging & logging"
msgstr ""

#: includes/ui/settings.php:94
msgid "Cache prefetching and analytics"
msgstr ""

#: includes/ui/settings.php:95
msgid "Fully unit tested (100% code coverage)"
msgstr ""

#: includes/ui/settings.php:96
msgid "Secure connections with TLS"
msgstr ""

#: includes/ui/settings.php:97
msgid "Health checks via WordPress & WP CLI"
msgstr ""

#: includes/ui/settings.php:98
msgid "Optimized for WooCommerce, Jetpack & Yoast SEO"
msgstr ""

#: includes/ui/settings.php:102
msgid "Learn more"
msgstr ""

#: includes/ui/settings.php:116
msgid "Your site meets the system requirements for the Pro version."
msgstr ""

#: includes/ui/settings.php:123
msgid "Your site <i>does not</i> meet the requirements for the Pro version:"
msgstr ""

#. translators: %s = PHP Version.
#: includes/ui/settings.php:132
msgid "The current version of PHP (%s) is too old. PHP 7.2 or newer is required."
msgstr ""

#: includes/ui/settings.php:141
msgid "The PhpRedis extension is not installed."
msgstr ""

#. translators: %s = Version of the PhpRedis extension.
#: includes/ui/settings.php:148
msgid "The current version of the PhpRedis extension (%s) is too old. PhpRedis 3.1.1 or newer is required."
msgstr ""

#: includes/ui/tabs/diagnostics.php:19
msgid "Copy diagnostics to clipboard"
msgstr ""

#: includes/ui/tabs/metrics.php:16
#: includes/ui/widget.php:17
msgid "The total amount of time (in milliseconds) it took Redis to return cache data."
msgstr ""

#: includes/ui/tabs/metrics.php:21
#: includes/ui/widget.php:22
msgid "The total amount of bytes that was retrieved from Redis."
msgstr ""

#: includes/ui/tabs/metrics.php:26
#: includes/ui/widget.php:27
msgid "The hit/miss ratio of cache data that was already cached."
msgstr ""

#: includes/ui/tabs/metrics.php:31
#: includes/ui/widget.php:32
msgid "The total amount of commands sent to Redis."
msgstr ""

#: includes/ui/tabs/overview.php:27
msgid "Redis is unreachable:"
msgstr ""

#: includes/ui/tabs/overview.php:36
msgid "Status:"
msgstr ""

#: includes/ui/tabs/overview.php:53
msgid "Filesystem:"
msgstr ""

#: includes/ui/tabs/overview.php:64
msgid "Not writeable"
msgstr ""

#: includes/ui/tabs/overview.php:70
msgid "Writeable"
msgstr ""

#: includes/ui/tabs/overview.php:77
msgid "Redis:"
msgstr ""

#: includes/ui/tabs/overview.php:82
msgid "Reachable"
msgstr ""

#: includes/ui/tabs/overview.php:87
msgid "Unreachable"
msgstr ""

#: includes/ui/tabs/overview.php:95
msgid "Key Prefix:"
msgstr ""

#: includes/ui/tabs/overview.php:104
msgid "Max. TTL:"
msgstr ""

#: includes/ui/tabs/overview.php:110
msgid "This doesn’t appear to be a valid number."
msgstr ""

#: includes/ui/tabs/overview.php:122
msgid "Connection"
msgstr ""

#: includes/ui/tabs/overview.php:129
msgid "Client:"
msgstr ""

#: includes/ui/tabs/overview.php:138
msgid "Host:"
msgstr ""

#: includes/ui/tabs/overview.php:145
msgid "Cluster:"
msgstr ""

#: includes/ui/tabs/overview.php:158
msgid "Shards:"
msgstr ""

#: includes/ui/tabs/overview.php:171
msgid "Servers:"
msgstr ""

#: includes/ui/tabs/overview.php:184
msgid "Port:"
msgstr ""

#: includes/ui/tabs/overview.php:191
msgid "Username:"
msgstr ""

#: includes/ui/tabs/overview.php:198
msgid "Password:"
msgstr ""

#: includes/ui/tabs/overview.php:207
msgid "Database:"
msgstr ""

#: includes/ui/tabs/overview.php:214
msgid "Connection Timeout:"
msgstr ""

#. translators: %s = Redis connection/read timeout in seconds.
#: includes/ui/tabs/overview.php:220
#: includes/ui/tabs/overview.php:237
msgid "%ss"
msgstr ""

#: includes/ui/tabs/overview.php:231
msgid "Read Timeout:"
msgstr ""

#: includes/ui/tabs/overview.php:248
msgid "Retry Interval:"
msgstr ""

#. translators: %s = Redis retry interval in milliseconds.
#: includes/ui/tabs/overview.php:254
msgid "%sms"
msgstr ""

#: includes/ui/tabs/overview.php:265
msgid "Redis Version:"
msgstr ""

#: includes/ui/tabs/overview.php:285
msgid "Disable Object Cache"
msgstr ""

#: includes/ui/tabs/overview.php:291
#: includes/ui/tabs/overview.php:295
msgid "Enable Object Cache"
msgstr ""
