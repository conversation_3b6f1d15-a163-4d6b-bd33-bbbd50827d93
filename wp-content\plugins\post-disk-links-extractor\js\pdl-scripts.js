jQuery(document).ready(function($) {
    // 初始化剪贴板功能
    var clipboard = new ClipboardJS('.copy-code-btn');
    
    // 成功复制回调
    clipboard.on('success', function(e) {
        var originalText = $(e.trigger).text();
        $(e.trigger).text('已复制!');
        
        // 2秒后恢复原始文本
        setTimeout(function() {
            $(e.trigger).text(originalText);
        }, 2000);
        
        e.clearSelection();
    });
    
    // 复制失败回调
    clipboard.on('error', function(e) {
        var originalText = $(e.trigger).text();
        $(e.trigger).text('复制失败，请手动复制');
        
        // 2秒后恢复原始文本
        setTimeout(function() {
            $(e.trigger).text(originalText);
        }, 2000);
    });
});