# Twitter API 代理助手

此插件为 WordPress 提供了通过韩国 AWS 服务器代理连接 Twitter API 的功能。

## 功能

- 自动为所有 Twitter API 请求添加 SOCKS5 代理设置
- 提供测试工具验证代理连接
- 修复 Twitter OAuth 授权和令牌刷新问题
- 修复 Twitter 自动发布插件的连接问题

## 代理设置

当前配置使用以下韩国 AWS 服务器作为代理：
```
socks5://13.209.96.45:7890
```

## 使用方法

1. 安装并激活此插件
2. 访问 "设置 > Twitter代理测试" 页面测试代理连接
3. 如需手动处理授权问题，访问 "设置 > Twitter令牌修复"

## 测试代理连接

您可以通过访问 `/wp-content/plugins/twitter-proxy-helper/proxy-test.php` 来快速测试代理连接。

或者使用以下命令在服务器上测试：

```bash
curl -v --proxy socks5://13.209.96.45:7890 https://api.twitter.com/2/openapi.json
```

## 常见问题

### Q: 无法连接到代理服务器

**原因**: 韩国 AWS 服务器可能暂时不可用或网络连接问题。

**解决方案**: 确认代理服务器在线且可访问，检查端口 7890 是否开放。

### Q: 仍然遇到 Twitter API 发布限制

**原因**: Twitter API 有严格的发布频率限制，即使使用了代理。

**解决方案**: 调整发布间隔，建议至少间隔 15-20 分钟。

## 版本历史

- 1.3: 更新配置使用韩国 AWS 服务器作为专用代理
- 1.2: 修复代理类型设置和多个稳定性改进
- 1.1: 增加代理自动切换功能
- 1.0: 初始版本

## 注意事项

- 此插件仅修改 Twitter API 请求的代理设置，不会影响站点的其他功能
- 需确保韩国 AWS 服务器持续在线并配置正确
- 任何代理问题请联系服务器管理员 