div.tiny_dashboard_widget div.inside {
	min-height: 200px;
	margin: 0;
	padding-bottom: 0;
	overflow: hidden;
}

div.tiny_dashboard_widget div.inside div.spinner {
	position: absolute;
	visibility: visible;
	z-index: 10;
	top: 30%;
	left: 50%;
	margin-left: -12px;
}

div.tiny_dashboard_widget div.inside div.sky {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	opacity: 0.3;
	background: linear-gradient(180deg, #c6dde4 0%, #fefeff 100%);
	/* ie6-9 */ filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#33C6DDE4', endColorstr='#33FEFEFF',GradientType=0 );
}

div.tiny_dashboard_widget div.inside div.cloud {
	position: absolute;
	right: 0;
	top: 0;
	height: 240px;
	width: 298px;
	background-image: url("../images/cloud.png");
	background-size: cover;
}

div.tiny_dashboard_widget div.inside div.grass {
	position: absolute;
	right: 0;
	bottom: 0;
	height: 24px;
	width: 536px;
	background-image: url("../images/grass.png");
	background-size: cover;
}

div.tiny_dashboard_widget div.inside div.panda {
	position: absolute;
	bottom: 0;
	background-repeat: no-repeat;
	-webkit-background-size: 100%;
	background-size: cover;
	background-position: bottom;
}

div.tiny_dashboard_widget.full-optimized div.inside div.panda {
	right: 10%;
	width: 305px;
	height: 105.5px;
	margin-right: 0;
	background-image: url("../images/panda-laying.png");
}

div.tiny_dashboard_widget.half-optimized div.inside div.panda {
	right: -230px;
	width: 255px;
	height: 203px;
	margin-right: 45%;
	background-image: url("../images/panda-eating.png");
}

div.tiny_dashboard_widget.not-optimized div.inside div.panda,
div.tiny_dashboard_widget.no-images-uploaded div.inside div.panda {
	right: -196px;
	width: 241px;
	height: 203px;
	margin-right: 45%;
	background-image: url("../images/panda-waiting.png");
}

div.tiny_dashboard_widget.no-images-uploaded div.inside div.no-images,
div.tiny_dashboard_widget.not-optimized div.inside div.not-optimized,
div.tiny_dashboard_widget.half-optimized div.inside div.half-optimized,
div.tiny_dashboard_widget.full-optimized div.inside div.full-optimized {
	display: block !important;
	position: relative;
	z-index: 1;
}

div.tiny_dashboard_widget div.inside div#optimization-chart {
	margin-top: 12px;
	margin-bottom: 40px;
	min-width: 160px;
}

div.tiny_dashboard_widget.full-optimized div.inside.mobile div#optimization-chart {
	width: 100%;
	margin-top: -5px;
	margin-bottom: 95px;
}

.ie8 div.tiny_dashboard_widget div.inside div.panda {
	background: none;
}

.ie8 div.tiny_dashboard_widget div.inside div.grass {
	width: 100%;
	background-image: url("../images/grass-ie8.png");
	background-repeat: no-repeat;
	background-position: bottom;
}

.ie8 div.tiny_dashboard_widget div.inside div#optimization-chart {
	display: none;
}

.ie8 div.tiny_dashboard_widget.no-images-uploaded div.inside div#ie8-compressed,
.ie8 div.tiny_dashboard_widget.not-optimized div.inside div#ie8-compressed,
.ie8 div.tiny_dashboard_widget.half-optimized div.inside div#ie8-compressed,
.ie8 div.tiny_dashboard_widget.full-optimized div.inside div#ie8-compressed {
	display: block !important;
	position: relative;
	z-index: 1;
}

#tinypng_dashboard_widget > button.handlediv {
	margin-top: 0;
}
