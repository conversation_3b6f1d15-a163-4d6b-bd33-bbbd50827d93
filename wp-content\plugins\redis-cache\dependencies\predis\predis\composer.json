{"name": "predis/predis", "type": "library", "description": "A flexible and feature-complete Redis client for PHP.", "keywords": ["nosql", "redis", "predis"], "homepage": "http://github.com/predis/predis", "license": "MIT", "support": {"issues": "https://github.com/predis/predis/issues"}, "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/tillkruss"}], "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ~9.4.4"}, "scripts": {"phpstan": "phpstan analyse", "style": "php-cs-fixer fix --diff --dry-run", "style:fix": "php-cs-fixer fix"}, "autoload": {"psr-4": {"Predis\\": "src/"}}, "config": {"sort-packages": true, "preferred-install": "dist"}, "minimum-stability": "dev", "prefer-stable": true}