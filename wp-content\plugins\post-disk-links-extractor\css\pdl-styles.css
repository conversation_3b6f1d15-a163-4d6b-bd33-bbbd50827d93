.disk-links-container {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e5e5e5;
}

.disk-links-title {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 8px;
}

.disk-link-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.disk-link-item svg {
    margin-right: 10px;
    flex-shrink: 0;
}

.disk-link-item a {
    color: #333;
    text-decoration: none;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.disk-link-item a:hover {
    color: #1e88e5;
}

.disk-link-code {
    display: flex;
    align-items: center;
    margin-top: 5px;
    margin-left: 34px;
    padding: 4px 10px;
    background: #fff;
    border-radius: 4px;
    border: 1px dashed #ddd;
}

.extraction-code {
    font-family: monospace;
    font-weight: bold;
    color: #e53935;
    margin: 0 10px;
}

.copy-code-btn {
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 2px 8px;
    cursor: pointer;
    font-size: 12px;
}

.copy-code-btn:hover {
    background: #e0e0e0;
}

.quark-link svg path {
    fill: #F8BD24;
}

.baidu-link svg path {
    fill: #2B5EEE;
}

@media (max-width: 768px) {
    .disk-link-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .disk-link-code {
        margin-left: 0;
        margin-top: 8px;
        width: 100%;
    }
}