name: Setup PHP
description: Setup PHP environment, cache dependencies, and install dependencies
runs:
  using: "composite"
  steps:
    - name: Set up PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ inputs.php }}
        tools: composer

    - name: Cache Composer Dependencies
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-

    - name: Install Dependencies
      shell: bash
      run: composer install --prefer-dist --no-progress --no-suggest

inputs:
  php:
    description: PHP version
    required: true