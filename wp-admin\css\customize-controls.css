body {
	overflow: hidden;
	-webkit-text-size-adjust: 100%;
}

.customize-controls-close,
.widget-control-actions a {
	text-decoration: none;
}

#customize-controls h3 {
	font-size: 14px;
}

#customize-controls img {
	max-width: 100%;
}

#customize-controls .submit {
	text-align: center;
}

#customize-controls #customize-notifications-area .notice.notification-overlay.notification-changeset-locked {
	background-color: rgba(0, 0, 0, 0.7);
	padding: 25px;
}

#customize-controls #customize-notifications-area .notice.notification-overlay.notification-changeset-locked .customize-changeset-locked-message {
	margin-left: auto;
	margin-right: auto;
	max-width: 366px;
	min-height: 64px;
	width: auto;
	padding: 25px;
	position: relative;
	background: #fff;
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
	line-height: 1.5;
	overflow-y: auto;
	text-align: left;
	top: calc( 50% - 100px );
}

#customize-controls #customize-notifications-area .notice.notification-overlay.notification-changeset-locked .customize-changeset-locked-message.has-avatar {
	padding-left: 109px;
}

#customize-controls #customize-notifications-area .notice.notification-overlay.notification-changeset-locked .currently-editing {
	margin-top: 0;
}
#customize-controls #customize-notifications-area .notice.notification-overlay.notification-changeset-locked .action-buttons {
	margin-bottom: 0;
}

.customize-changeset-locked-avatar {
	width: 64px;
	position: absolute;
	left: 25px;
	top: 25px;
}

.wp-core-ui.wp-customizer .customize-changeset-locked-message a.button {
	margin-right: 10px;
	margin-top: 0;
}

#customize-controls .description {
	color: #50575e;
}

#customize-save-button-wrapper {
	float: right;
	margin-top: 9px;
}

body:not(.ready) #customize-save-button-wrapper .save {
	visibility: hidden;
}
#customize-save-button-wrapper .save {
	float: left;
	border-radius: 3px;
	box-shadow: none; /* @todo Adjust box shadow based on the disable states of paired button. */
	margin-top: 0;
}

#customize-save-button-wrapper .save:focus, #publish-settings:focus {
	box-shadow: 0 1px 0 #2271b1, 0 0 2px 1px #72aee6; /* This is default box shadow for focus */
}

#customize-save-button-wrapper .save.has-next-sibling {
	border-radius: 3px 0 0 3px;
}

#customize-sidebar-outer-content {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	visibility: hidden;
	overflow-x: hidden;
	overflow-y: auto;
	width: 100%;
	margin: 0;
	z-index: -1;
	background: #f0f0f1;
	transition: left .18s;
	border-right: 1px solid #dcdcde;
	border-left: 1px solid #dcdcde;
	height: 100%;
}

@media (prefers-reduced-motion: reduce) {
	#customize-sidebar-outer-content {
		transition: none;
	}
}

#customize-theme-controls .control-section-outer {
	display: none !important;
}

#customize-outer-theme-controls .accordion-section-content {
	padding: 12px;
}

#customize-outer-theme-controls .accordion-section-content.open {
	display: block;
}

.outer-section-open .wp-full-overlay.expanded #customize-sidebar-outer-content {
	visibility: visible;
	left: 100%;
	transition: left .18s;
}

@media (prefers-reduced-motion: reduce) {
	.outer-section-open .wp-full-overlay.expanded #customize-sidebar-outer-content {
		transition: none;
	}
}

.customize-outer-pane-parent {
	margin: 0;
}

.outer-section-open .wp-full-overlay.expanded .wp-full-overlay-main {
	left: 300px;
	opacity: 0.4;
}

.outer-section-open .wp-full-overlay.expanded.preview-tablet .wp-full-overlay-main,
.outer-section-open .wp-full-overlay.expanded.preview-mobile .wp-full-overlay-main,
.adding-menu-items .wp-full-overlay.expanded.preview-tablet .wp-full-overlay-main,
.adding-menu-items .wp-full-overlay.expanded.preview-mobile .wp-full-overlay-main,
.adding-widget .wp-full-overlay.expanded.preview-tablet .wp-full-overlay-main,
.adding-widget .wp-full-overlay.expanded.preview-mobile .wp-full-overlay-main {
	left: 64%;
}

#customize-outer-theme-controls li.notice {
	padding-top: 8px;
	padding-bottom: 8px;
	margin-left: 0;
	margin-bottom: 10px;
}

#publish-settings {
	text-indent: 0;
	border-radius: 0 3px 3px 0;
	padding-left: 0;
	padding-right: 0;
	box-shadow: none; /* @todo Adjust box shadow based on the disable states of paired button. */
	font-size: 14px;
	width: 30px;
	float: left;
	transform: none;
	margin-top: 0;
	line-height: 2;
}

body:not(.ready) #publish-settings,
body.trashing #customize-save-button-wrapper .save,
body.trashing #publish-settings {
	display: none;
}

#customize-header-actions .spinner {
	margin-top: 13px;
	margin-right: 4px;
}

.saving #customize-header-actions .spinner,
.trashing #customize-header-actions .spinner {
	visibility: visible;
}

#customize-header-actions {
	border-bottom: 1px solid #dcdcde;
}

#customize-controls .wp-full-overlay-sidebar-content {
	overflow-y: auto;
	overflow-x: hidden;
}

.outer-section-open #customize-controls .wp-full-overlay-sidebar-content {
	background: #f0f0f1;
}

#customize-controls .customize-info {
	border: none;
	border-bottom: 1px solid #dcdcde;
	margin-bottom: 15px;
}

#customize-control-changeset_status .customize-inside-control-row,
#customize-control-changeset_preview_link input {
	background-color: #fff;
	border-bottom: 1px solid #dcdcde;
	box-sizing: content-box;
	width: 100%;
	margin-left: -12px;
	padding-left: 12px;
	padding-right: 12px;
}

#customize-control-trash_changeset {
	margin-top: 20px;
}
#customize-control-trash_changeset .button-link {
	position: relative;
	padding-left: 24px;
	display: inline-block;
}
#customize-control-trash_changeset .button-link:before {
	content: "\f182";
	font: normal 22px dashicons;
	text-decoration: none;
	position: absolute;
	left: 0;
	top: -2px;
}

#customize-controls .date-input:invalid {
	border-color: #d63638;
}

#customize-control-changeset_status .customize-inside-control-row {
	padding-top: 10px;
	padding-bottom: 10px;
	font-weight: 500;
}

#customize-control-changeset_status .customize-inside-control-row:first-of-type {
	border-top: 1px solid #dcdcde;
}

#customize-control-changeset_status .customize-control-title {
	margin-bottom: 6px;
}

#customize-control-changeset_status input {
	margin-left: 0;
}

#customize-control-changeset_preview_link {
	position: relative;
	display: block;
}

.preview-link-wrapper .customize-copy-preview-link.preview-control-element.button {
	margin: 0;
	position: absolute;
	bottom: 9px;
	right: 0;
}

.preview-link-wrapper {
	position: relative;
}

.customize-copy-preview-link:before,
.customize-copy-preview-link:after {
	content: "";
	height: 28px;
	position: absolute;
	background: #fff;
	top: -1px;
}

.customize-copy-preview-link:before {
	left: -10px;
	width: 9px;
	opacity: 0.75;
}

.customize-copy-preview-link:after {
	left: -5px;
	width: 4px;
	opacity: 0.8;
}

#customize-control-changeset_preview_link input {
	line-height: 2.85714286; /* 40px */
	border-top: 1px solid #dcdcde;
	border-left: none;
	border-right: none;
	text-indent: -999px;
	color: #fff;
	/* Only necessary for IE11 */
	min-height: 40px;
}

#customize-control-changeset_preview_link label {
	position: relative;
	display: block;
}

#customize-control-changeset_preview_link a {
	display: inline-block;
	position: absolute;
	white-space: nowrap;
	overflow: hidden;
	width: 90%;
	bottom: 14px;
	font-size: 14px;
	text-decoration: none;
}

#customize-control-changeset_preview_link a.disabled,
#customize-control-changeset_preview_link a.disabled:active,
#customize-control-changeset_preview_link a.disabled:focus,
#customize-control-changeset_preview_link a.disabled:visited {
	color: #000;
	opacity: 0.4;
	cursor: default;
	outline: none;
	box-shadow: none;
}

#sub-accordion-section-publish_settings .customize-section-description-container {
	display: none;
}

#customize-controls .customize-info.section-meta {
	margin-bottom: 15px;
}

.customize-control-date_time .customize-control-description + .date-time-fields.includes-time {
	margin-top: 10px;
}

.customize-control.customize-control-date_time .date-time-fields .date-input.day {
	margin-right: 0;
}

.date-time-fields .date-input.month {
	width: auto;
	margin: 0;
}

.date-time-fields .date-input.day,
.date-time-fields .date-input.hour,
.date-time-fields .date-input.minute {
	width: 46px;
}

.date-time-fields .date-input.year {
	width: 65px;
}

.date-time-fields .date-input.meridian {
	width: auto;
	margin: 0;
}

.date-time-fields .time-row {
	margin-top: 12px;
}

#customize-control-changeset_preview_link {
	margin-top: 6px;
}

#customize-control-changeset_status {
	margin-bottom: 0;
	padding-bottom: 0;
}

#customize-control-changeset_scheduled_date {
	box-sizing: content-box;
	width: 100%;
	margin-left: -12px;
	padding: 12px;
	background: #fff;
	border-bottom: 1px solid #dcdcde;
	margin-bottom: 0;
}

#customize-control-site_icon .customize-control-description,
#customize-control-changeset_scheduled_date .customize-control-description {
	font-style: normal;
}

#customize-controls .customize-info.is-in-view,
#customize-controls .customize-section-title.is-in-view {
	position: absolute;
	z-index: 9;
	width: 100%;
	box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
}

#customize-controls .customize-section-title.is-in-view {
	margin-top: 0;
}

#customize-controls .customize-info.is-in-view + .accordion-section {
	margin-top: 15px;
}

#customize-controls .customize-info.is-sticky,
#customize-controls .customize-section-title.is-sticky {
	position: fixed;
	top: 46px;
}

#customize-controls .customize-info .accordion-section-title {
	background: #fff;
	color: #50575e;
	border-left: none;
	border-right: none;
	border-bottom: none;
	cursor: default;
}

#customize-controls .customize-info.open .accordion-section-title:after,
#customize-controls .customize-info .accordion-section-title:hover:after,
#customize-controls .customize-info .accordion-section-title:focus:after {
	color: #2c3338;
}

#customize-controls .customize-info .accordion-section-title:after {
	display: none;
}

#customize-controls .customize-info .preview-notice {
	font-size: 13px;
	line-height: 1.9;
}

#customize-controls .customize-pane-child .customize-section-title h3,
#customize-controls .customize-pane-child h3.customize-section-title,
#customize-outer-theme-controls .customize-pane-child .customize-section-title h3,
#customize-outer-theme-controls .customize-pane-child h3.customize-section-title,
#customize-controls .customize-info .panel-title {
	font-size: 20px;
	font-weight: 200;
	line-height: 26px;
	display: block;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

#customize-controls .customize-section-title span.customize-action {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

#customize-controls .customize-info .customize-help-toggle {
	position: absolute;
	top: 4px;
	right: 1px;
	padding: 20px 20px 10px 10px;
	width: 20px;
	height: 20px;
	cursor: pointer;
	box-shadow: none;
	background: transparent;
	color: #50575e;
	border: none;
}

#customize-controls .customize-info .customize-help-toggle:before {
	position: absolute;
	top: 5px;
	left: 6px;
}

#customize-controls .customize-info.open .customize-help-toggle,
#customize-controls .customize-info .customize-help-toggle:focus,
#customize-controls .customize-info .customize-help-toggle:hover {
	color: #2271b1;
}

#customize-controls .customize-info .customize-panel-description,
#customize-controls .customize-info .customize-section-description,
#customize-outer-theme-controls .customize-info .customize-section-description,
#customize-controls .no-widget-areas-rendered-notice {
	color: #50575e;
	display: none;
	background: #fff;
	padding: 12px 15px;
	border-top: 1px solid #dcdcde;
}

#customize-controls .customize-info .customize-panel-description.open + .no-widget-areas-rendered-notice {
	border-top: none;
}
.no-widget-areas-rendered-notice {
	font-style: italic;
}
.no-widget-areas-rendered-notice p:first-child {
	margin-top: 0;
}
.no-widget-areas-rendered-notice p:last-child {
	margin-bottom: 0;
}

#customize-controls .customize-info .customize-section-description {
	margin-bottom: 15px;
}

#customize-controls .customize-info .customize-panel-description p:first-child,
#customize-controls .customize-info .customize-section-description p:first-child {
	margin-top: 0;
}

#customize-controls .customize-info .customize-panel-description p:last-child,
#customize-controls .customize-info .customize-section-description p:last-child {
	margin-bottom: 0;
}

#customize-controls .current-panel .control-section > h3.accordion-section-title {
	padding-right: 30px;
}

#customize-theme-controls .control-section,
#customize-outer-theme-controls .control-section {
	border: none;
}

#customize-theme-controls .accordion-section-title,
#customize-outer-theme-controls .accordion-section-title {
	color: #50575e;
	background-color: #fff;
	border-bottom: 1px solid #dcdcde;
	border-left: 4px solid #fff;
	transition:
		.15s color ease-in-out,
		.15s background-color ease-in-out,
		.15s border-color ease-in-out;
}

.accordion-section-title:has(button.accordion-trigger),
#customize-controls .current-panel .control-section > h3.accordion-section-title:has(button.accordion-trigger) {
	padding: 0;
}

.accordion-section-title button.accordion-trigger {
	all: unset;
	width: 100%;
	height: 100%;
	padding: 10px 30px 11px 14px;
	display: flex;
	align-items: center;
	box-sizing: border-box;
}

.accordion-section-title button.accordion-trigger:has(.menu-in-location) {
	display: block;
}

@media (prefers-reduced-motion: reduce) {
	#customize-theme-controls .accordion-section-title,
	#customize-outer-theme-controls .accordion-section-title {
		transition: none;
	}
}

#customize-controls #customize-theme-controls .customize-themes-panel .accordion-section-title {
	color: #50575e;
	background-color: #fff;
	border-left: 4px solid #fff;
}

#customize-theme-controls .accordion-section-title:after,
#customize-outer-theme-controls .accordion-section-title:after {
	content: "\f345";
	color: #a7aaad;
	pointer-events: none;
}

#customize-theme-controls .accordion-section-content,
#customize-outer-theme-controls .accordion-section-content {
	color: #50575e;
	background: transparent;
}

#customize-controls .control-section:hover > .accordion-section-title,
#customize-controls .control-section .accordion-section-title button:hover,
#customize-controls .control-section.open .accordion-section-title,
#customize-controls .control-section .accordion-section-title button:focus {
	color: #2271b1;
	background: #f6f7f7;
	border-left-color: #2271b1;
}

#accordion-section-themes + .control-section {
	border-top: 1px solid #dcdcde;
}

.js .control-section:hover .accordion-section-title,
.js .control-section .accordion-section-title:hover,
.js .control-section.open .accordion-section-title,
.js .control-section .accordion-section-title:focus {
	background: #f6f7f7;
}

#customize-theme-controls .control-section:hover > .accordion-section-title:after,
#customize-theme-controls .control-section .accordion-section-title:hover:after,
#customize-theme-controls .control-section.open .accordion-section-title:after,
#customize-theme-controls .control-section .accordion-section-title:focus:after,
#customize-outer-theme-controls .control-section:hover > .accordion-section-title:after,
#customize-outer-theme-controls .control-section .accordion-section-title:hover:after,
#customize-outer-theme-controls .control-section.open .accordion-section-title:after,
#customize-outer-theme-controls .control-section .accordion-section-title:focus:after {
	color: #2271b1;
}

#customize-theme-controls .control-section.open {
	border-bottom: 1px solid #f0f0f1;
}

#customize-theme-controls .control-section.open .accordion-section-title,
#customize-outer-theme-controls .control-section.open .accordion-section-title {
	border-bottom-color: #f0f0f1 !important;
}

#customize-theme-controls .control-section:last-of-type.open,
#customize-theme-controls .control-section:last-of-type > .accordion-section-title {
	border-bottom-color: #dcdcde;
}

#customize-theme-controls .control-panel-content:not(.control-panel-nav_menus) .control-section:nth-child(2),
#customize-theme-controls .control-panel-nav_menus .control-section-nav_menu,
#customize-theme-controls .control-section-nav_menu_locations .accordion-section-title {
	border-top: 1px solid #dcdcde;
}

#customize-theme-controls .control-panel-nav_menus .control-section-nav_menu + .control-section-nav_menu {
	border-top: none;
}

#customize-theme-controls > ul {
	margin: 0;
}

#customize-theme-controls .accordion-section-content {
	position: absolute;
	top: 0;
	left: 100%;
	width: 100%;
	margin: 0;
	padding: 12px;
	box-sizing: border-box;
}

#customize-info,
#customize-theme-controls .customize-pane-parent,
#customize-theme-controls .customize-pane-child {
	overflow: visible;
	width: 100%;
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	transition: 0.18s transform cubic-bezier(0.645, 0.045, 0.355, 1); /* easeInOutCubic */
}

@media (prefers-reduced-motion: reduce) {
	#customize-info,
	#customize-theme-controls .customize-pane-parent,
	#customize-theme-controls .customize-pane-child {
		transition: none;
	}
}

#customize-theme-controls .customize-pane-child.skip-transition {
	transition: none;
}

#customize-info,
#customize-theme-controls .customize-pane-parent {
	position: relative;
	visibility: visible;
	height: auto;
	max-height: none;
	overflow: auto;
	transform: none;
}

#customize-theme-controls .customize-pane-child {
	position: absolute;
	top: 0;
	left: 0;
	visibility: hidden;
	height: 0;
	max-height: none;
	overflow: hidden;
	transform: translateX(100%);
}

#customize-theme-controls .customize-pane-child.open,
#customize-theme-controls .customize-pane-child.current-panel {
	transform: none;
}

.section-open #customize-theme-controls .customize-pane-parent,
.in-sub-panel #customize-theme-controls .customize-pane-parent,
.section-open #customize-info,
.in-sub-panel #customize-info,
.in-sub-panel.section-open #customize-theme-controls .customize-pane-child.current-panel {
	visibility: hidden;
	height: 0;
	overflow: hidden;
	transform: translateX(-100%);
}

.section-open #customize-theme-controls .customize-pane-parent.busy,
.in-sub-panel #customize-theme-controls .customize-pane-parent.busy,
.section-open #customize-info.busy,
.in-sub-panel #customize-info.busy,
.busy.section-open.in-sub-panel #customize-theme-controls .customize-pane-child.current-panel,
#customize-theme-controls .customize-pane-child.open,
#customize-theme-controls .customize-pane-child.current-panel,
#customize-theme-controls .customize-pane-child.busy {
	visibility: visible;
	height: auto;
	overflow: auto;
}

#customize-theme-controls .customize-pane-child.accordion-section-content,
#customize-theme-controls .customize-pane-child.accordion-sub-container {
	display: block;
	overflow-x: hidden;
}

#customize-theme-controls .customize-pane-child.accordion-section-content {
	padding: 12px;
}

#customize-theme-controls .customize-pane-child.menu li {
	position: static;
}

.customize-section-description-container,
.control-section-nav_menu .customize-section-description-container,
.control-section-new_menu .customize-section-description-container {
	margin-bottom: 15px;
}

.control-section-nav_menu .customize-control,
.control-section-new_menu .customize-control {
	/* Override default `margin-bottom` for `.customize-control` */
	margin-bottom: 0;
}

.customize-section-title {
	margin: -12px -12px 0;
	border-bottom: 1px solid #dcdcde;
	background: #fff;
}

div.customize-section-description {
	margin-top: 22px;
}

.customize-info div.customize-section-description {
	margin-top: 0;
}

div.customize-section-description p:first-child {
	margin-top: 0;
}

div.customize-section-description p:last-child {
	margin-bottom: 0;
}

#customize-theme-controls .customize-themes-panel h3.customize-section-title:first-child {
	border-bottom: 1px solid #dcdcde;
	padding: 12px;
}

.ios #customize-theme-controls .customize-themes-panel h3.customize-section-title:first-child {
	padding: 12px 12px 13px;
}

.customize-section-title h3,
h3.customize-section-title {
	padding: 10px 10px 12px 14px;
	margin: 0;
	line-height: 21px;
	color: #50575e;
}

.accordion-sub-container.control-panel-content {
	display: none;
	position: absolute;
	top: 0;
	width: 100%;
}

.accordion-sub-container.control-panel-content.busy {
	display: block;
}

.current-panel .accordion-sub-container.control-panel-content {
	width: 100%;
}

.customize-controls-close {
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	width: 45px;
	height: 41px;
	padding: 0 2px 0 0;
	background: #f0f0f1;
	border: none;
	border-top: 4px solid #f0f0f1;
	border-right: 1px solid #dcdcde;
	color: #3c434a;
	text-align: left;
	cursor: pointer;
	transition:
		color .15s ease-in-out,
		border-color .15s ease-in-out,
		background .15s ease-in-out;
	box-sizing: content-box;
}

.customize-panel-back,
.customize-section-back {
	display: block;
	float: left;
	width: 48px;
	height: 71px;
	padding: 0 24px 0 0;
	margin: 0;
	background: #fff;
	border: none;
	border-right: 1px solid #dcdcde;
	border-left: 4px solid #fff;
	box-shadow: none;
	cursor: pointer;
	transition:
		color .15s ease-in-out,
		border-color .15s ease-in-out,
		background .15s ease-in-out;
}

.customize-section-back {
	height: 74px;
}

.ios .customize-panel-back {
	display: none;
}

.ios .expanded.in-sub-panel .customize-panel-back {
	display: block;
}

#customize-controls .panel-meta.customize-info .accordion-section-title {
	margin-left: 48px;
	border-left: none;
}

#customize-controls .panel-meta.customize-info .accordion-section-title:hover,
#customize-controls .cannot-expand:hover .accordion-section-title {
	background: #fff;
	color: #50575e;
	border-left-color: #fff;
}

.customize-controls-close:focus,
.customize-controls-close:hover,
.customize-controls-preview-toggle:focus,
.customize-controls-preview-toggle:hover {
	background: #fff;
	color: #2271b1;
	border-top-color: #2271b1;
	box-shadow: none;
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
}

#customize-theme-controls .accordion-section-title:focus .customize-action {
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
	outline-offset: 1px;
}

.customize-panel-back:hover,
.customize-panel-back:focus,
.customize-section-back:hover,
.customize-section-back:focus {
	color: #2271b1;
	background: #f6f7f7;
	border-left-color: #2271b1;
	box-shadow: none;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
	outline-offset: -2px;
}

.customize-controls-close:before {
	font: normal 22px/45px dashicons;
	content: "\f335";
	position: relative;
	top: -3px;
	left: 13px;
}

.customize-panel-back:before,
.customize-section-back:before {
	font: normal 20px/72px dashicons;
	content: "\f341";
	position: relative;
	left: 9px;
}

.wp-full-overlay-sidebar .wp-full-overlay-header {
	background-color: #f0f0f1;
	transition: padding ease-in-out .18s;
}

.in-sub-panel .wp-full-overlay-sidebar .wp-full-overlay-header {
	padding-left: 62px;
}

p.customize-section-description {
	font-style: normal;
	margin-top: 22px;
	margin-bottom: 0;
}

.customize-section-description ul {
	margin-left: 1em;
}

.customize-section-description ul > li {
	list-style: disc;
}

.section-description-buttons {
	text-align: right;
}

.customize-control {
	width: 100%;
	float: left;
	clear: both;
	margin-bottom: 12px;
}

.customize-control input[type="text"],
.customize-control input[type="password"],
.customize-control input[type="email"],
.customize-control input[type="number"],
.customize-control input[type="search"],
.customize-control input[type="tel"],
.customize-control input[type="url"],
.customize-control input[type="range"] {
	width: 100%;
	margin: 0;
}

.customize-control-hidden {
	margin: 0;
}

.customize-control-textarea textarea {
	width: 100%;
	resize: vertical;
}

.customize-control select {
	width: 100%;
}

.customize-control select[multiple] {
	height: auto;
}

.customize-control-title {
	display: block;
	font-size: 14px;
	line-height: 1.75;
	font-weight: 600;
	margin-bottom: 4px;
}

.customize-control-description {
	display: block;
	font-style: italic;
	line-height: 1.4;
	margin-top: 0;
	margin-bottom: 5px;
}

.customize-section-description a.external-link:after {
	font: 16px/11px dashicons;
	content: "\f504";
	top: 3px;
	position: relative;
	padding-left: 3px;
	display: inline-block;
	text-decoration: none;
}

.customize-control-color .color-picker,
.customize-control-upload div {
	line-height: 28px;
}

.customize-control .customize-inside-control-row {
	line-height: 1.6;
	display: block;
	margin-left: 24px;
	padding-top: 6px;
	padding-bottom: 6px;
}

.customize-control-radio input,
.customize-control-checkbox input,
.customize-control-nav_menu_auto_add input {
	margin-right: 4px;
	margin-left: -24px;
}

.customize-control-radio {
	padding: 5px 0 10px;
}

.customize-control-radio .customize-control-title {
	margin-bottom: 0;
	line-height: 1.6;
}

.customize-control-radio .customize-control-title + .customize-control-description {
	margin-top: 7px;
}

.customize-control-radio label,
.customize-control-checkbox label {
	vertical-align: top;
}

.customize-control .attachment-thumb.type-icon {
	float: left;
	margin: 10px;
	width: auto;
}

.customize-control .attachment-title {
	font-weight: 600;
	margin: 0;
	padding: 5px 10px;
}

.customize-control .attachment-meta {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin: 0;
	padding: 0 10px;
}

.customize-control .attachment-meta-title {
	padding-top: 7px;
}

/* Remove descender space. */
.customize-control .thumbnail-image,
.customize-control-header .current,
.customize-control .wp-media-wrapper.wp-video {
	line-height: 0;
}


.customize-control .thumbnail-image img {
	cursor: pointer;
}

#customize-controls .thumbnail-audio .thumbnail {
	max-width: 64px;
	max-height: 64px;
	margin: 10px;
	float: left;
}

#available-menu-items .accordion-section-content .new-content-item-wrapper,
.customize-control-dropdown-pages .new-content-item-wrapper {
	width: calc(100% - 30px);
	padding: 8px 15px;
	position: absolute;
	bottom: 0;
	z-index: 10;
	background: #f0f0f1;
}

.customize-control-dropdown-pages .new-content-item-wrapper {
	width: 100%;
	padding: 0;
	position: static;
}

#available-menu-items .accordion-section-content .new-content-item,
.customize-control-dropdown-pages .new-content-item {
	display: flex;
}

.customize-control-dropdown-pages .new-content-item {
	width: 100%;
	padding: 5px 0 5px 1px;
	position: relative;
}

.customize-control-dropdown-pages .new-content-item-wrapper .new-content-item {
	padding: 0;
}

.customize-control-dropdown-pages .new-content-item-wrapper .new-content-item label {
	line-height: 1.6;
}

#available-menu-items .new-content-item .create-item-input,
.customize-control-dropdown-pages .new-content-item .create-item-input {
	flex-grow: 10;
}

#available-menu-items .new-content-item .add-content,
.customize-control-dropdown-pages .new-content-item .add-content {
	margin: 2px 0 2px 6px;
	flex-grow: 1;
}

.customize-control-dropdown-pages .new-content-item .create-item-input.invalid {
	border: 1px solid #d63638;
}

.customize-control-dropdown-pages .add-new-toggle {
	margin-left: 1px;
	font-weight: 600;
	line-height: 2.2;
}

#customize-preview iframe {
	width: 100%;
	height: 100%;
	position: absolute;
}
#customize-preview iframe + iframe {
	visibility: hidden;
}

.wp-full-overlay-sidebar {
	background: #f0f0f1;
	border-right: 1px solid #dcdcde;
}


/**
 * Notifications
 */

#customize-controls .customize-control-notifications-container { /* Scoped to #customize-controls for specificity over notification styles in common.css. */
	margin: 4px 0 8px;
	padding: 0;
	cursor: default;
}

#customize-controls .customize-control-widget_form.has-error .widget .widget-top,
.customize-control-nav_menu_item.has-error .menu-item-bar .menu-item-handle {
	box-shadow: inset 0 0 0 2px #d63638;
	transition: .15s box-shadow linear;
}

#customize-controls .customize-control-notifications-container li.notice {
	list-style: none;
	margin: 0 0 6px;
	padding: 9px 14px;
	overflow: hidden;
}
#customize-controls .customize-control-notifications-container .notice.is-dismissible {
	padding-right: 38px;
}

.customize-control-notifications-container li.notice:last-child {
	margin-bottom: 0;
}

#customize-controls .customize-control-nav_menu_item .customize-control-notifications-container {
	margin-top: 0;
}

#customize-controls .customize-control-widget_form .customize-control-notifications-container {
	margin-top: 8px;
}

.customize-control-text.has-error input {
	outline: 2px solid #d63638;
}

#customize-controls #customize-notifications-area {
	position: absolute;
	top: 46px;
	width: 100%;
	border-bottom: 1px solid #dcdcde;
	display: block;
	padding: 0;
	margin: 0;
}

.wp-full-overlay.collapsed #customize-controls #customize-notifications-area {
	display: none !important;
}

#customize-controls #customize-notifications-area:not(.has-overlay-notifications),
#customize-controls .customize-section-title > .customize-control-notifications-container:not(.has-overlay-notifications),
#customize-controls .panel-meta > .customize-control-notifications-container:not(.has-overlay-notifications) {
	max-height: 210px;
	overflow-x: hidden;
	overflow-y: auto;
}

#customize-controls #customize-notifications-area > ul,
#customize-controls #customize-notifications-area .notice,
#customize-controls .panel-meta > .customize-control-notifications-container,
#customize-controls .panel-meta > .customize-control-notifications-container .notice,
#customize-controls .customize-section-title > .customize-control-notifications-container,
#customize-controls .customize-section-title > .customize-control-notifications-container .notice {
	margin: 0;
}
#customize-controls .panel-meta > .customize-control-notifications-container,
#customize-controls .customize-section-title > .customize-control-notifications-container {
	border-top: 1px solid #dcdcde;
}
#customize-controls #customize-notifications-area .notice,
#customize-controls .panel-meta > .customize-control-notifications-container .notice,
#customize-controls .customize-section-title > .customize-control-notifications-container .notice {
	padding: 9px 14px;
}
#customize-controls #customize-notifications-area .notice.is-dismissible,
#customize-controls .panel-meta > .customize-control-notifications-container .notice.is-dismissible,
#customize-controls .customize-section-title > .customize-control-notifications-container .notice.is-dismissible {
	padding-right: 38px;
}
#customize-controls #customize-notifications-area .notice + .notice,
#customize-controls .panel-meta > .customize-control-notifications-container .notice + .notice,
#customize-controls .customize-section-title > .customize-control-notifications-container .notice + .notice {
	margin-top: 1px;
}

@keyframes customize-fade-in {
	0%   { opacity: 0; }
	100% { opacity: 1; }
}

#customize-controls .notice.notification-overlay,
#customize-controls #customize-notifications-area .notice.notification-overlay {
	margin: 0;
	border-left: 0; /* @todo Appropriate styles could be added for notice-error, notice-warning, notice-success, etc */
}

#customize-controls .customize-control-notifications-container.has-overlay-notifications {
	animation: customize-fade-in 0.5s;
	z-index: 30;
}

/* Note: Styles for this are also defined in themes.css */
#customize-controls #customize-notifications-area .notice.notification-overlay .notification-message {
	clear: both;
	color: #1d2327;
	font-size: 18px;
	font-style: normal;
	margin: 0;
	padding: 2em 0;
	text-align: center;
	width: 100%;
	display: block;
	top: 50%;
	position: relative;
}

/* Style for custom settings */

/**
 * Static front page
 */

#customize-control-show_on_front.has-error {
	margin-bottom: 0;
}
#customize-control-show_on_front.has-error .customize-control-notifications-container {
	margin-top: 12px;
}

/**
 * Dropdowns
 */

.accordion-section .dropdown {
	float: left;
	display: block;
	position: relative;
	cursor: pointer;
}

.accordion-section .dropdown-content {
	overflow: hidden;
	float: left;
	min-width: 30px;
	height: 16px;
	line-height: 16px;
	margin-right: 16px;
	padding: 4px 5px;
	border: 2px solid #f0f0f1;
	-webkit-user-select: none;
	user-select: none;
}

/* @todo maybe no more used? */
.customize-control .dropdown-arrow {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	width: 20px;
	background: #f0f0f1;
}

.customize-control .dropdown-arrow:after {
	content: "\f140";
	font: normal 20px/1 dashicons;
	speak: never;
	display: block;
	padding: 0;
	text-indent: 0;
	text-align: center;
	position: relative;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none !important;
	color: #2c3338;
}

.customize-control .dropdown-status {
	color: #2c3338;
	background: #f0f0f1;
	display: none;
	max-width: 112px;
}

.customize-control-color .dropdown {
	margin-right: 5px;
	margin-bottom: 5px;
}

.customize-control-color .dropdown .dropdown-content {
	background-color: #50575e;
	border: 1px solid rgba(0, 0, 0, 0.15);
}

.customize-control-color .dropdown:hover .dropdown-content {
	border-color: rgba(0, 0, 0, 0.25);
}

/**
 * iOS can't scroll iframes,
 * instead it expands the iframe size to match the size of the content
 */

.ios .wp-full-overlay {
	position: relative;
}

.ios #customize-controls .wp-full-overlay-sidebar-content {
	-webkit-overflow-scrolling: touch;
}

/* Media controls */

.customize-control .actions .button {
	margin-top: 12px;
}

.customize-control-header .actions,
.customize-control-header .uploaded {
	margin-bottom: 18px;
}

.customize-control-header .uploaded button:not(.random),
.customize-control-header .default button:not(.random) {
	width: 100%;
	padding: 0;
	margin: 0;
	background: none;
	border: none;
	color: inherit;
	cursor: pointer;
}

.customize-control-header button img {
	display: block;
}

.customize-control .attachment-media-view .remove-button,
.customize-control .attachment-media-view .default-button,
.customize-control .attachment-media-view .upload-button,
.customize-control-header button.new,
.customize-control-header button.remove {
	width: auto;
	height: auto;
	white-space: normal;
}

.customize-control .attachment-media-view .thumbnail,
.customize-control-header .current .container {
	overflow: hidden;
}

.customize-control .attachment-media-view .placeholder,
.customize-control .attachment-media-view .button-add-media,
.customize-control-header .placeholder {
	width: 100%;
	position: relative;
	text-align: center;
	cursor: default;
	border: 1px dashed #c3c4c7;
	box-sizing: border-box;
	padding: 9px 0;
	line-height: 1.6;
}

.customize-control .attachment-media-view .button-add-media {
	cursor: pointer;
	background-color: #f0f0f1;
	color: #2c3338;
}

.customize-control .attachment-media-view .button-add-media:hover {
	background-color: #fff;
}

.customize-control .attachment-media-view .button-add-media:focus {
	background-color: #fff;
	border-color: #3582c4;
	border-style: solid;
	box-shadow: 0 0 0 1px #3582c4;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.customize-control-header .inner {
	display: none;
	position: absolute;
	width: 100%;
	color: #50575e;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.customize-control-header .inner,
.customize-control-header .inner .dashicons {
	line-height: 20px;
	top: 8px;
}

.customize-control-header .list .inner,
.customize-control-header .list .inner .dashicons {
	top: 9px;
}

.customize-control-header .header-view {
	position: relative;
	width: 100%;
	margin-bottom: 12px;
}

.customize-control-header .header-view:last-child {
	margin-bottom: 0;
}

/* Convoluted, but 'outline' support isn't good enough yet */
.customize-control-header .header-view:after {
	border: 0;
}

.customize-control-header .header-view.selected .choice:focus {
	outline: none;
}

.customize-control-header .header-view.selected:after {
	content: "";
	position: absolute;
	height: auto;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	border: 4px solid #72aee6;
	border-radius: 2px;
}

.customize-control-header .header-view.button.selected {
	border: 0;
}

/* Header control: overlay "close" button */

.customize-control-header .uploaded .header-view .close {
	font-size: 20px;
	color: #fff;
	background: #50575e;
	background: rgba(0, 0, 0, 0.5);
	position: absolute;
	top: 10px;
	left: -999px;
	z-index: 1;
	width: 26px;
	height: 26px;
	cursor: pointer;
}

.customize-control-header .header-view:hover .close,
.customize-control-header .header-view .close:focus {
	left: auto;
	right: 10px;
}

.customize-control-header .header-view .close:focus {
	outline: 1px solid #4f94d4;
}

/* Header control: randomiz(s)er */

.customize-control-header .random.placeholder {
	cursor: pointer;
	border-radius: 2px;
	height: 40px;
}

.customize-control-header button.random {
	width: 100%;
	height: auto;
	min-height: 40px;
	white-space: normal;
}

.customize-control-header button.random .dice {
	margin-top: 4px;
}

.customize-control-header .placeholder:hover .dice,
.customize-control-header .header-view:hover > button.random .dice {
	animation: dice-color-change 3s infinite;
}

.button-see-me {
	animation: bounce .7s 1;
	transform-origin: center bottom;
}

@keyframes bounce {
	from, 20%, 53%, 80%, to {
		animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
		transform: translate3d(0,0,0);
	}

	40%, 43% {
		animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
		transform: translate3d(0, -12px, 0);
	}

	70% {
		animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
		transform: translate3d(0, -6px, 0);
	}

	90% {
		transform: translate3d(0,-1px,0);
	}
}

.customize-control-header .choice {
	position: relative;
	display: block;
	margin-bottom: 9px;
}

.customize-control-header .choice:focus {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.customize-control-header .uploaded div:last-child > .choice {
	margin-bottom: 0;
}

.customize-control .attachment-media-view .thumbnail-image img,
.customize-control-header img {
	max-width: 100%;
}

.customize-control .attachment-media-view .remove-button,
.customize-control .attachment-media-view .default-button,
.customize-control-header .remove {
	margin-right: 8px;
}

/* Background position control */
.customize-control-background_position .background-position-control .button-group {
	display: block;
}

/**
 * Code Editor Control and Custom CSS Section
 *
 * Modifications to the Section Container to make the textarea full-width and
 * full-height, if the control is the only control in the section.
 */

.customize-control-code_editor textarea {
	width: 100%;
	font-family: Consolas, Monaco, monospace;
	font-size: 12px;
	padding: 6px 8px;
	tab-size: 2;
}
.customize-control-code_editor textarea,
.customize-control-code_editor .CodeMirror {
	height: 14em;
}

#customize-controls .customize-section-description-container.section-meta.customize-info {
	border-bottom: none;
}

#sub-accordion-section-custom_css .customize-control-notifications-container {
	margin-bottom: 15px;
}

#customize-control-custom_css textarea {
	display: block;
	height: 500px;
}

.customize-section-description-container + #customize-control-custom_css .customize-control-title {
	margin-left: 12px;
}

.customize-section-description-container + #customize-control-custom_css:last-child textarea {
	border-right: 0;
	border-left: 0;
	height: calc( 100vh - 185px );
	resize: none;
}

.customize-section-description-container + #customize-control-custom_css:last-child {
	margin-left: -12px;
	width: 299px;
	width: calc( 100% + 24px );
	margin-bottom: -12px;
}

.customize-section-description-container + #customize-control-custom_css:last-child .CodeMirror {
	height: calc( 100vh - 185px );
}

.CodeMirror-lint-tooltip,
.CodeMirror-hints {
	z-index: 500000 !important;
}

.customize-section-description-container + #customize-control-custom_css:last-child .customize-control-notifications-container {
	margin-left: 12px;
	margin-right: 12px;
}

.theme-browser .theme.active .theme-actions,
.wp-customizer .theme-browser .theme .theme-actions {
	padding: 9px 15px;
	box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 640px) {
	.customize-section-description-container + #customize-control-custom_css:last-child {
		margin-right: 0;
	}

	.customize-section-description-container + #customize-control-custom_css:last-child textarea {
		height: calc( 100vh - 140px );
	}
}

/**
 * Themes
 */

#customize-theme-controls .control-panel-themes {
	border-bottom: none;
}

#customize-theme-controls .control-panel-themes > .accordion-section-title:hover, /* Not a focusable element. */
#customize-theme-controls .control-panel-themes > .accordion-section-title {
	cursor: default;
	background: #fff;
	color: #50575e;
	border-top: 1px solid #dcdcde;
	border-bottom: 1px solid #dcdcde;
	border-left: none;
	border-right: none;
	margin: 0 0 15px;
	padding-right: 100px; /* Space for the button */
}

#customize-theme-controls .control-section-themes .customize-themes-panel .accordion-section-title:first-child:hover, /* Not a focusable element. */
#customize-theme-controls .control-section-themes .customize-themes-panel .accordion-section-title:first-child {
	border-top: 0;
}

#customize-theme-controls .control-section-themes > .accordion-section-title:hover, /* Not a focusable element. */
#customize-theme-controls .control-section-themes > .accordion-section-title {
	margin: 0 0 15px;
}

#customize-controls .customize-themes-panel .accordion-section-title:hover,
#customize-controls .customize-themes-panel .accordion-section-title {
	margin: 15px -8px;
}

#customize-controls .control-section-themes .accordion-section-title,
#customize-controls .customize-themes-panel .accordion-section-title {
	padding-right: 100px; /* Space for the button */
}

.control-panel-themes .accordion-section-title span.customize-action,
#customize-controls .customize-section-title span.customize-action,
#customize-controls .control-section-themes .accordion-section-title span.customize-action,
#customize-controls .customize-section-title span.customize-action {
	font-size: 13px;
	display: block;
	font-weight: 400;
}

#customize-theme-controls .control-panel-themes .accordion-section-title .change-theme {
	position: absolute;
	right: 10px;
	top: 50%;
	margin-top: -14px;
	font-weight: 400;
}

#customize-notifications-area .notification-message button.switch-to-editor {
	display: block;
	margin-top: 6px;
	font-weight: 400;
}

#customize-theme-controls .control-panel-themes > .accordion-section-title:after {
	display: none;
}

.control-panel-themes .customize-themes-full-container {
	position: fixed;
	top: 0;
	left: 0;
	transition: .18s left ease-in-out;
	margin: 0 0 0 300px;
	padding: 71px 0 25px;
	overflow-y: scroll;
	width: calc(100% - 300px);
	height: calc(100% - 96px);
	background: #f0f0f1;
	z-index: 20;
}

@media (prefers-reduced-motion: reduce) {
	.control-panel-themes .customize-themes-full-container {
		transition: none;
	}
}

@media screen and (min-width: 1670px) {
	.control-panel-themes .customize-themes-full-container {
		width: 82%;
		right: 0;
		left: initial;
	}
}

.modal-open .control-panel-themes .customize-themes-full-container {
	overflow-y: visible;
}

/* Animations for opening the themes panel */
#customize-save-button-wrapper,
#customize-header-actions .spinner,
#customize-header-actions .customize-controls-preview-toggle {
	transition: .18s margin ease-in-out;
}

#customize-footer-actions,
#customize-footer-actions .collapse-sidebar {
	bottom: 0;
	transition: .18s bottom ease-in-out;
}

.in-themes-panel:not(.animating) #customize-header-actions .spinner,
.in-themes-panel:not(.animating) #customize-header-actions .customize-controls-preview-toggle,
.in-themes-panel:not(.animating) #customize-preview,
.in-themes-panel:not(.animating) #customize-footer-actions {
	visibility: hidden;
}

.wp-full-overlay.in-themes-panel {
	background: #f0f0f1; /* Prevents a black flash when fading in the panel */
}

.in-themes-panel #customize-save-button-wrapper,
.in-themes-panel #customize-header-actions .spinner,
.in-themes-panel #customize-header-actions .customize-controls-preview-toggle {
	margin-top: -46px; /* Height of header actions bar */
}

.in-themes-panel #customize-footer-actions,
.in-themes-panel #customize-footer-actions .collapse-sidebar {
	bottom: -45px;
}

/* Don't show the theme count while the panel opens, as it's in the wrong place during the animation */
.in-themes-panel.animating .control-panel-themes .filter-themes-count {
	display: none;
}

.in-themes-panel.wp-full-overlay .wp-full-overlay-sidebar-content {
	bottom: 0;
}

.themes-filter-bar .feature-filter-toggle:before {
	content: "\f111";
	margin: 0 5px 0 0;
	font: normal 16px/1 dashicons;
	vertical-align: text-bottom;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.themes-filter-bar .feature-filter-toggle.open {
	background: #f0f0f1;
	border-color: #8c8f94;
	box-shadow: inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
}

.themes-filter-bar .feature-filter-toggle .filter-count-filters {
	display: none;
}

.filter-drawer {
	box-sizing: border-box;
	width: 100%;
	position: absolute;
	top: 46px;
	left: 0;
	padding: 25px 0 25px 25px;
	border-top: 0;
	margin: 0;
	background: #f0f0f1;
	border-bottom: 1px solid #dcdcde;
}

.filter-drawer .filter-group {
	margin: 0 25px 0 0;
	width: calc( (100% - 75px) / 3);
	min-width: 200px;
	max-width: 320px;
}

/* Adds a delay before fading in to avoid it "jumping" */
@keyframes themes-fade-in {
	0% {
		opacity: 0;
	}
	50% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}

.control-panel-themes .customize-themes-full-container.animate {
	animation: .6s themes-fade-in 1;
}

.in-themes-panel:not(.animating) .control-panel-themes .filter-themes-count {
	animation: .6s themes-fade-in 1;
}

.control-panel-themes .filter-themes-count .themes-displayed {
	font-weight: 600;
	color: #50575e;
}

.customize-themes-notifications {
	margin: 0;
}

.control-panel-themes .customize-themes-notifications .notice {
	margin: 0 0 25px;
}

.customize-themes-full-container .customize-themes-section {
	display: none !important; /* There is unknown JS that perpetually tries to show all theme sections when more items are added. */
	overflow: hidden;
}

.customize-themes-full-container .customize-themes-section.current-section {
	display: list-item !important; /* There is unknown JS that perpetually tries to show all theme sections when more items are added. */
}

.control-section .customize-section-text-before {
	padding: 0 0 8px 15px;
	margin: 15px 0 0;
	line-height: 16px;
	border-bottom: 1px solid #dcdcde;
	color: #50575e;
}

.control-panel-themes .customize-themes-section-title {
	width: 100%;
	background: #fff;
	box-shadow: none;
	outline: none;
	border-top: none;
	border-bottom: 1px solid #dcdcde;
	border-left: 4px solid #fff;
	border-right: none;
	cursor: pointer;
	padding: 10px 15px;
	position: relative;
	text-align: left;
	font-size: 14px;
	font-weight: 600;
	color: #50575e;
	text-shadow: none;
}

.control-panel-themes #accordion-section-installed_themes {
	border-top: 1px solid #dcdcde;
}

.control-panel-themes .theme-section {
	margin: 0;
	position: relative;
}

.control-panel-themes .customize-themes-section-title:focus,
.control-panel-themes .customize-themes-section-title:hover {
	border-left-color: #2271b1;
	color: #2271b1;
	background: #f6f7f7;
}

.customize-themes-section-title:not(.selected):after {
	content: "";
	display: block;
	position: absolute;
	top: 9px;
	right: 15px;
	width: 18px;
	height: 18px;
	border-radius: 100%;
	border: 1px solid #c3c4c7;
	background: #fff;
}

.control-panel-themes .theme-section .customize-themes-section-title.selected:after {
	content: "\f147";
	font: 16px/1 dashicons;
	box-sizing: border-box;
	width: 20px;
	height: 20px;
	padding: 3px 3px 1px 1px; /* Re-align the icon to the smaller grid */
	border-radius: 100%;
	position: absolute;
	top: 9px;
	right: 15px;
	background: #2271b1;
	color: #fff;
}

.control-panel-themes .customize-themes-section-title.selected {
	color: #2271b1;
}

#customize-theme-controls .themes.accordion-section-content {
	position: relative;
	left: 0;
	padding: 0;
	width: 100%;
}

.loading .customize-themes-section .spinner {
	display: block;
	visibility: visible;
	position: relative;
	clear: both;
	width: 20px;
	height: 20px;
	left: calc(50% - 10px);
	float: none;
	margin-top: 50px;
}

.customize-themes-section .no-themes,
.customize-themes-section .no-themes-local {
	display: none;
}

.themes-section-installed_themes .theme .notice-success:not(.updated-message) {
	display: none; /* Hide "installed" notice on installed themes tab. */
}

.customize-control-theme .theme {
	width: 100%;
	margin: 0;
	border: 1px solid #dcdcde;
	background: #fff;
}

.customize-control-theme .theme .theme-name, .customize-control-theme .theme .theme-actions {
	background: #fff;
	border: none;
}

.customize-control.customize-control-theme { /* override most properties on .customize-control */
	box-sizing: border-box;
	width: 25%;
	max-width: 600px; /* Max. screenshot size / 2 */
	margin: 0 25px 25px 0;
	padding: 0;
	clear: none;
}

/* 5 columns above 2100px */
@media screen and (min-width: 2101px) {
	.customize-control.customize-control-theme {
		width: calc( ( 100% - 125px ) / 5 - 1px ); /* 1px offset accounts for browser rounding, typical all grids */
	}
}

/* 4 columns up to 2100px */
@media screen and (min-width: 1601px) and (max-width: 2100px) {
	.customize-control.customize-control-theme {
		width: calc( ( 100% - 100px ) / 4 - 1px );
	}
}

/* 3 columns up to 1600px */
@media screen and (min-width: 1201px) and (max-width: 1600px) {
	.customize-control.customize-control-theme {
		width: calc( ( 100% - 75px ) / 3 - 1px );
	}
}

/* 2 columns up to 1200px */
@media screen and (min-width: 851px) and (max-width: 1200px) {
	.customize-control.customize-control-theme {
		width: calc( ( 100% - 50px ) / 2 - 1px );

	}
}

/* 1 column up to 850 px */
@media screen and (max-width: 850px) {
	.customize-control.customize-control-theme {
		width: 100%;
	}
}

.wp-customizer .theme-browser .themes {
	padding: 0 0 25px 25px;
	transition: .18s margin-top linear;
}

.wp-customizer .theme-browser .theme .theme-actions {
	opacity: 1;
}

#customize-controls h3.theme-name {
	font-size: 15px;
}

#customize-controls .theme-overlay .theme-name {
	font-size: 32px;
}

.customize-preview-header.themes-filter-bar {
	position: fixed;
	top: 0;
	left: 300px;
	width: calc(100% - 300px);
	height: 46px;
	background: #f0f0f1;
	z-index: 10;
	padding: 6px 25px;
	box-sizing: border-box;
	border-bottom: 1px solid #dcdcde;
}
.customize-preview-header.themes-filter-bar,
.customize-preview-header.themes-filter-bar .search-form {
	display: flex;
	align-items: center;
	gap: 10px;
	flex-wrap: wrap;
}

.customize-preview-header.themes-filter-bar .search-form-input {
	position: relative;
}

.customize-preview-header .filter-themes-wrapper {
	display: grid;
	align-items: center;
	gap: 10px;
	grid-template-columns: auto 1fr;
}

.customize-preview-header .filter-themes-wrapper .filter-themes-count {
	justify-self: end;
}

@media screen and (min-width: 1670px) {
	.customize-preview-header.themes-filter-bar {
		width: 82%;
		right: 0;
		left: initial;
	}
}

.themes-filter-bar .themes-filter-container {
	margin: 0;
	padding: 0;
	display: flex;
	align-items: center;
	gap: 10px;
}

.themes-filter-bar .wp-filter-search {
	line-height: 1.8;
	padding: 6px 10px 6px 30px;
	max-width: 100%;
	width: 40%;
	min-width: 300px;
	height: 32px;
	margin: 1px 0;
	top: 0;
	left: 0;
}

/* Unstick the filter bar on short windows/screens. This breakpoint is based on the
   current length of .org feature filters assuming translations do not wrap lines. */
@media screen and (max-height: 540px), screen and (max-width: 1018px) {
	.customize-preview-header.themes-filter-bar {
		position: relative;
		left: 0;
		width: 100%;
		margin: 0 0 25px;
	}
	.filter-drawer {
		top: 46px;
	}
	.wp-customizer .theme-browser .themes {
		padding: 0 0 25px 25px;
		overflow: hidden;
	}

	.control-panel-themes .customize-themes-full-container {
		margin-top: 0;
		padding: 0;
		height: 100%;
		width: calc(100% - 300px);
	}
}

@media screen and (max-width: 1018px) {
	.filter-drawer .filter-group {
		width: calc( (100% - 50px) / 2);
	}
}

@media screen and (max-width: 960px) {
	.customize-preview-header.themes-filter-bar {
		height: 96px;
	}
}

@media screen and (max-width: 900px) {
	.themes-filter-bar .wp-filter-search {
		width: 100%;
		margin: 0;
		min-width: 200px;
	}

	.customize-preview-header.themes-filter-bar,
	.customize-preview-header.themes-filter-bar .search-form
	.themes-filter-bar .themes-filter-container {
		display: grid;
		gap: 4px;
	}

	.customize-preview-header.themes-filter-bar .search-form-input {
		display: flex;
		flex-grow: 1;
	}

	.filter-drawer {
		top: 86px;
	}

	.control-panel-themes .filter-themes-count {
		float: left;
	}
}

@media screen and (max-width: 792px) {
	.filter-drawer .filter-group {
		width: calc( 100% - 25px);
	}
}

.control-panel-themes .customize-themes-mobile-back {
	display: none;
}

/* Mobile - toggle between themes and filters */
@media screen and (max-width: 600px) {

	.filter-drawer {
		top: 132px;
	}

	.wp-full-overlay.showing-themes .control-panel-themes .filter-themes-count .filter-themes {
		display: block;
		float: right;
	}

	.control-panel-themes .customize-themes-full-container {
		width: 100%;
		margin: 0;
		padding-top: 46px;
		height: calc(100% - 46px);
		z-index: 1;
		display: none;
	}

	.showing-themes .control-panel-themes .customize-themes-full-container {
		display: block;
	}

	.wp-customizer .showing-themes .control-panel-themes .customize-themes-mobile-back {
		display: block;
		position: fixed;
		top: 0;
		left: 0;
		background: #f0f0f1;
		color: #3c434a;
		border-radius: 0;
		box-shadow: none;
		border: none;
		height: 46px;
		width: 100%;
		z-index: 10;
		text-align: left;
		text-shadow: none;
		border-bottom: 1px solid #dcdcde;
		border-left: 4px solid transparent;
		margin: 0;
		padding: 0;
		font-size: 0;
		overflow: hidden;
	}

	.wp-customizer .showing-themes .control-panel-themes .customize-themes-mobile-back:before {
		left: 0;
		top: 0;
		height: 46px;
		width: 26px;
		display: block;
		line-height: 2.3;
		padding: 0 8px;
		border-right: 1px solid #dcdcde;
	}

	.wp-customizer .showing-themes .control-panel-themes .customize-themes-mobile-back:hover,
	.wp-customizer .showing-themes .control-panel-themes .customize-themes-mobile-back:focus {
		color: #2271b1;
		background: #f6f7f7;
		border-left-color: #2271b1;
		box-shadow: none;
		/* Only visible in Windows High Contrast mode */
		outline: 2px solid transparent;
		outline-offset: -2px;
	}

	.showing-themes #customize-header-actions {
		display: none;
	}

	#customize-controls {
		width: 100%;
	}
}

/* Details View */
.wp-customizer .theme-overlay {
	display: none;
}

.wp-customizer.modal-open .theme-overlay {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	z-index: 109;
}

/* Avoid a z-index war by resetting elements that should be under the overlay.
   This is likely required because of the way that sections and panels are positioned. */
.wp-customizer.modal-open #customize-header-actions,
.wp-customizer.modal-open .control-panel-themes .filter-themes-count,
.wp-customizer.modal-open .control-panel-themes .customize-themes-section-title.selected:after {
	z-index: -1;
}

.wp-full-overlay.in-themes-panel.themes-panel-expanded #customize-controls .wp-full-overlay-sidebar-content {
	overflow: visible;
}

.wp-customizer .theme-overlay .theme-backdrop {
	background: rgba(240, 240, 241, 0.75);
	position: fixed;
	z-index: 110;
}

.wp-customizer .theme-overlay .star-rating {
	float: left;
	margin-right: 8px;
}

.wp-customizer .theme-rating .num-ratings {
	line-height: 20px;
}

.wp-customizer .theme-overlay .theme-wrap {
	left: 90px;
	right: 90px;
	top: 45px;
	bottom: 45px;
	z-index: 120;
}

.wp-customizer .theme-overlay .theme-actions {
	text-align: right; /* Because there're only one or two actions, match the UI pattern of media modals and right-align the action. */
	padding: 10px 25px 5px;
	background: #f0f0f1;
	border-top: 1px solid #dcdcde;
}

.wp-customizer .theme-overlay .theme-actions .theme-install.preview {
	margin-left: 8px;
}

.modal-open .in-themes-panel #customize-controls .wp-full-overlay-sidebar-content {
	overflow: visible; /* Prevent the top-level Customizer controls from becoming visible when elements on the right of the details modal are focused. */
}

.wp-customizer .theme-header {
	background: #f0f0f1;
}

.wp-customizer .theme-overlay .theme-header button,
.wp-customizer .theme-overlay .theme-header .close:before {
	color: #3c434a;
}

.wp-customizer .theme-overlay .theme-header .close:focus,
.wp-customizer .theme-overlay .theme-header .close:hover,
.wp-customizer .theme-overlay .theme-header .right:focus,
.wp-customizer .theme-overlay .theme-header .right:hover,
.wp-customizer .theme-overlay .theme-header .left:focus,
.wp-customizer .theme-overlay .theme-header .left:hover {
	background: #fff;
	border-bottom: 4px solid #2271b1;
	color: #2271b1;
}

.wp-customizer .theme-overlay .theme-header .close:focus:before,
.wp-customizer .theme-overlay .theme-header .close:hover:before {
	color: #2271b1;
}

.wp-customizer .theme-overlay .theme-header button.disabled,
.wp-customizer .theme-overlay .theme-header button.disabled:hover,
.wp-customizer .theme-overlay .theme-header button.disabled:focus {
	border-bottom: none;
	background: transparent;
	color: #c3c4c7;
}

/* Small Screens */
@media (max-width: 850px), (max-height: 472px) {
	.wp-customizer .theme-overlay .theme-wrap {
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
	}

	.wp-customizer .theme-browser .themes {
		padding-right: 25px;
	}
}

/* Handle cheaters. */
body.cheatin {
	font-size: medium;
	height: auto;
	background: #fff;
	border: 1px solid #c3c4c7;
	margin: 50px auto 2em;
	padding: 1em 2em;
	max-width: 700px;
	min-width: 0;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

body.cheatin h1 {
	border-bottom: 1px solid #dcdcde;
	clear: both;
	color: #50575e;
	font-size: 24px;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	margin: 30px 0 0;
	padding: 0 0 7px;
}

body.cheatin p {
	font-size: 14px;
	line-height: 1.5;
	margin: 25px 0 20px;
}

/**
 * Widgets and Menus common styles
 */

/* higher specificity than .wp-core-ui .button */
#customize-theme-controls .add-new-widget,
#customize-theme-controls .add-new-menu-item {
	cursor: pointer;
	float: right;
	margin: 0 0 0 10px;
	transition: all 0.2s;
	-webkit-user-select: none;
	user-select: none;
	outline: none;
}

.reordering .add-new-widget,
.reordering .add-new-menu-item {
	opacity: 0.2;
	pointer-events: none;
	cursor: not-allowed; /* doesn't work in conjunction with pointer-events */
}

.add-new-widget:before,
.add-new-menu-item:before,
#available-menu-items .new-content-item .add-content:before {
	content: "\f132";
	display: inline-block;
	position: relative;
	left: -2px;
	top: 0;
	font: normal 20px/1 dashicons;
	vertical-align: middle;
	transition: all 0.2s;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Reordering */
.reorder-toggle {
	float: right;
	padding: 5px 8px;
	text-decoration: none;
	cursor: pointer;
	outline: none;
}

.reorder,
.reordering .reorder-done {
	display: block;
	padding: 5px 8px;
}

.reorder-done,
.reordering .reorder {
	display: none;
}

.widget-reorder-nav span,
.menu-item-reorder-nav button {
	position: relative;
	overflow: hidden;
	float: left;
	display: block;
	width: 33px; /* was 42px for mobile */
	height: 43px;
	color: #8c8f94;
	text-indent: -9999px;
	cursor: pointer;
	outline: none;
}

.menu-item-reorder-nav button {
	width: 30px;
	height: 40px;
	background: transparent;
	border: none;
	box-shadow: none;
}

.widget-reorder-nav span:before,
.menu-item-reorder-nav button:before {
	display: inline-block;
	position: absolute;
	top: 0;
	right: 0;
	width: 100%;
	height: 100%;
	font: normal 20px/43px dashicons;
	text-align: center;
	text-indent: 0;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.widget-reorder-nav span:hover,
.widget-reorder-nav span:focus,
.menu-item-reorder-nav button:hover,
.menu-item-reorder-nav button:focus {
	color: #1d2327;
	background: #f0f0f1;
}

.move-widget-down:before,
.menus-move-down:before {
	content: "\f347";
}

.move-widget-up:before,
.menus-move-up:before {
	content: "\f343";
}

#customize-theme-controls .first-widget .move-widget-up,
#customize-theme-controls .last-widget .move-widget-down,
.move-up-disabled .menus-move-up,
.move-down-disabled .menus-move-down,
.move-right-disabled .menus-move-right,
.move-left-disabled .menus-move-left {
	color: #dcdcde;
	background-color: #fff;
	cursor: default;
	pointer-events: none;
}

/**
 * New widget and Add-menu-items modes and panels
 */

.wp-full-overlay-main {
	right: auto; /* this overrides a right: 0; which causes the preview to resize, I'd rather have it go off screen at the normal size. */
	width: 100%;
}

body.adding-widget .add-new-widget,
body.adding-widget .add-new-widget:hover,
.adding-menu-items .add-new-menu-item,
.adding-menu-items .add-new-menu-item:hover,
.add-menu-toggle.open,
.add-menu-toggle.open:hover {
	background: #f0f0f1;
	border-color: #8c8f94;
	color: #2c3338;
	box-shadow: inset 0 2px 5px -3px rgba(0, 0, 0, 0.5);
}

body.adding-widget .add-new-widget:before,
.adding-menu-items .add-new-menu-item:before,
#accordion-section-add_menu .add-new-menu-item.open:before {
	transform: rotate(45deg);
}

#available-widgets,
#available-menu-items {
	position: absolute;
	top: 0;
	bottom: 0;
	left: -301px;
	visibility: hidden;
	overflow-x: hidden;
	overflow-y: auto;
	width: 300px;
	margin: 0;
	z-index: 4;
	background: #f0f0f1;
	transition: left .18s;
	border-right: 1px solid #dcdcde;
}

#available-widgets .customize-section-title,
#available-menu-items .customize-section-title {
	display: none;
}

#available-widgets-list {
	top: 82px;
	position: absolute;
	overflow: auto;
	bottom: 0;
	width: 100%;
	border-top: 1px solid #dcdcde;
}

.no-widgets-found #available-widgets-list {
	border-top: none;
}

#available-widgets-filter {
	position: fixed;
	top: 0;
	z-index: 1;
	width: 300px;
	background: #f0f0f1;
}

/* search field container */
#available-widgets-filter,
#available-menu-items-search .accordion-section-title {
	padding: 13px 15px;
	box-sizing: border-box;
}

#available-widgets-filter input,
#available-menu-items-search input {
	width: 100%;
	min-height: 32px;
	margin: 1px 0;
	padding: 0 30px;
}

#available-widgets-filter input::-ms-clear,
#available-menu-items-search input::-ms-clear {
	display: none; /* remove the "x" in IE, which conflicts with the "x" icon on button.clear-results */
}

#available-menu-items-search .search-icon,
#available-widgets-filter .search-icon {
	display: block;
	position: absolute;
	bottom: 15px; /* 13 container padding +1 input margin +1 input border */
	left: 16px;
	width: 30px;
	height: 30px;
	line-height: 2.1;
	text-align: center;
	color: #646970;
}

#available-widgets-filter .clear-results,
#available-menu-items-search .accordion-section-title .clear-results {
	position: absolute;
	top: 36px; /* 13 container padding +1 input margin +1 input border */
	right: 16px;
	width: 30px;
	height: 30px;
	padding: 0;
	border: 0;
	cursor: pointer;
	background: none;
	color: #d63638;
	text-decoration: none;
	outline: 0;
}

#available-widgets-filter .clear-results,
#available-menu-items-search .clear-results,
#available-menu-items-search.loading .clear-results.is-visible {
	display: none;
}

#available-widgets-filter .clear-results.is-visible,
#available-menu-items-search .clear-results.is-visible {
	display: block;
}

#available-widgets-filter .clear-results:before,
#available-menu-items-search .clear-results:before {
	content: "\f335";
	font: normal 20px/1 dashicons;
	vertical-align: middle;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

#available-widgets-filter .clear-results:hover,
#available-widgets-filter .clear-results:focus,
#available-menu-items-search .clear-results:hover,
#available-menu-items-search .clear-results:focus {
	color: #d63638;
}

#available-widgets-filter .clear-results:focus,
#available-menu-items-search .clear-results:focus {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

#available-menu-items-search .search-icon:after,
#available-widgets-filter .search-icon:after,
.themes-filter-bar .search-icon:after {
	content: "\f179";
	font: normal 20px/1 dashicons;
	vertical-align: middle;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.themes-filter-bar .search-icon {
	position: absolute;
	top: 2px;
	left: 2px;
	z-index: 1;
	color: #646970;
	height: 30px;
	width: 30px;
	line-height: 2;
	text-align: center;
}

.no-widgets-found-message {
	display: none;
	margin: 0;
	padding: 0 15px;
	line-height: inherit;
}

.no-widgets-found .no-widgets-found-message {
	display: block;
}

#available-widgets .widget-top,
#available-widgets .widget-top:hover,
#available-menu-items .item-top,
#available-menu-items .item-top:hover {
	border: none;
	background: transparent;
	box-shadow: none;
}

#available-widgets .widget-tpl,
#available-menu-items .item-tpl {
	position: relative;
	padding: 15px 15px 15px 60px;
	background: #fff;
	border-bottom: 1px solid #dcdcde;
	border-left: 4px solid #fff;
	transition:
		.15s color ease-in-out,
		.15s background-color ease-in-out,
		.15s border-color ease-in-out;
	cursor: pointer;
	display: none;
}

#available-widgets .widget,
#available-menu-items .item {
	position: static;
}


/* Responsive */
.customize-controls-preview-toggle {
	display: none;
}

@media only screen and (max-width: 782px) {
	.wp-customizer .theme:not(.active):hover .theme-actions,
	.wp-customizer .theme:not(.active):focus .theme-actions {
		display: block;
	}

	.wp-customizer .theme-browser .theme.active .theme-name span {
		display: inline;
	}

	.customize-control-header button.random .dice {
		margin-top: 0;
	}

	.customize-control-radio .customize-inside-control-row,
	.customize-control-checkbox .customize-inside-control-row,
	.customize-control-nav_menu_auto_add .customize-inside-control-row {
		margin-left: 32px;
	}

	.customize-control-radio input,
	.customize-control-checkbox input,
	.customize-control-nav_menu_auto_add input {
		margin-left: -32px;
	}

	.customize-control input[type="radio"] + label + br,
	.customize-control input[type="checkbox"] + label + br {
		line-height: 2.5; /* For widgets checkboxes */
	}

	.customize-control .date-time-fields select {
		height: 39px;
	}

	.date-time-fields .date-input.month {
		width: 79px;
	}

	.date-time-fields .date-input.day,
	.date-time-fields .date-input.hour,
	.date-time-fields .date-input.minute {
		width: 55px;
	}

	.date-time-fields .date-input.year {
		width: 80px;
	}

	#customize-control-changeset_preview_link a {
		bottom: 16px;
	}

	.preview-link-wrapper .customize-copy-preview-link.preview-control-element.button {
		bottom: 10px;
	}

	.media-widget-control .media-widget-buttons .button.edit-media,
	.media-widget-control .media-widget-buttons .button.change-media,
	.media-widget-control .media-widget-buttons .button.select-media {
		margin-top: 12px;
	}

	.customize-preview-header.themes-filter-bar .search-icon {
		top: 6px;
	}
}

@media screen and (max-width: 1200px) {
	.outer-section-open .wp-full-overlay.expanded.preview-mobile .wp-full-overlay-main,
	.adding-menu-items .wp-full-overlay.expanded.preview-mobile .wp-full-overlay-main,
	.adding-widget .wp-full-overlay.expanded.preview-mobile .wp-full-overlay-main {
		left: 67%;
	}
}

@media screen and (max-width: 640px) {

	/* when the sidebar is collapsed and switching to responsive view,
	   bring it back see ticket #35220 */
	.wp-full-overlay.collapsed #customize-controls {
		margin-left: 0;
	}

	.wp-full-overlay-sidebar .wp-full-overlay-sidebar-content {
		bottom: 0;
	}

	.customize-controls-preview-toggle {
		display: block;
		position: absolute;
		top: 0;
		left: 48px;
		line-height: 2.6;
		font-size: 14px;
		padding: 0 12px 4px;
		margin: 0;
		height: 45px;
		background: #f0f0f1;
		border: 0;
		border-right: 1px solid #dcdcde;
		border-top: 4px solid #f0f0f1;
		color: #50575e;
		cursor: pointer;
		transition: color .1s ease-in-out, background .1s ease-in-out;
	}

	#customize-footer-actions,
	/*#customize-preview,*/
	.customize-controls-preview-toggle .controls,
	.preview-only .wp-full-overlay-sidebar-content,
	.preview-only .customize-controls-preview-toggle .preview {
		display: none;
	}

	.preview-only #customize-save-button-wrapper {
		margin-top: -46px;
	}

	.customize-controls-preview-toggle .preview:before,
	.customize-controls-preview-toggle .controls:before {
		font: normal 20px/1 dashicons;
		content: "\f177";
		position: relative;
		top: 4px;
		margin-right: 6px;
	}

	.customize-controls-preview-toggle .controls:before {
		content: "\f540";
	}

	.preview-only #customize-controls {
		height: 45px;
	}

	.preview-only #customize-preview,
	.preview-only .customize-controls-preview-toggle .controls {
		display: block;
	}

	.wp-core-ui.wp-customizer .button {
		min-height: 30px;
		padding: 0 14px;
		line-height: 2;
		font-size: 14px;
		vertical-align: middle;
	}

	#customize-control-changeset_status .customize-inside-control-row {
		padding-top: 15px;
	}

	body.adding-widget div#available-widgets,
	body.adding-menu-items div#available-menu-items,
	body.outer-section-open div#customize-sidebar-outer-content {
		width: 100%;
	}

	#available-widgets .customize-section-title,
	#available-menu-items .customize-section-title {
		display: block;
		margin: 0;
	}

	#available-widgets .customize-section-back,
	#available-menu-items .customize-section-back {
		height: 69px;
	}

	#available-widgets .customize-section-title h3,
	#available-menu-items .customize-section-title h3 {
		font-size: 20px;
		font-weight: 200;
		padding: 9px 10px 12px 14px;
		margin: 0;
		line-height: 24px;
		color: #50575e;
		display: block;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	#available-widgets .customize-section-title .customize-action,
	#available-menu-items .customize-section-title .customize-action {
		font-size: 13px;
		display: block;
		font-weight: 400;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	#available-widgets-filter {
		position: relative;
		width: 100%;
		height: auto;
	}

	#available-widgets-list {
		top: 152px;
	}

	#available-menu-items-search .clear-results {
		top: 36px;
		right: 16px;
	}

	.reorder,
	.reordering .reorder-done {
		padding: 8px;
	}
}

@media screen and (max-width: 600px) {
	.wp-full-overlay.expanded {
		margin-left: 0;
	}

	body.adding-widget div#available-widgets,
	body.adding-menu-items div#available-menu-items,
	body.outer-section-open div#customize-sidebar-outer-content {
		top: 46px;
		z-index: 10;
	}

	body.wp-customizer .wp-full-overlay.expanded #customize-sidebar-outer-content {
		left: -100%;
	}

	body.wp-customizer.outer-section-open .wp-full-overlay.expanded #customize-sidebar-outer-content {
		left: 0;
	}
}
