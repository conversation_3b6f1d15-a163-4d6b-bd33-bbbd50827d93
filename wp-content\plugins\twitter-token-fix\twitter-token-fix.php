<?php
/**
 * Plugin Name: Twitter Token Fix
 * Description: 修复Twitter令牌交换问题
 * Version: 2.0
 * Author: Admin
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    die;
}

/**
 * 获取韩国AWS代理服务器
 */
function twitter_token_fix_get_proxy() {
    if (function_exists('twitter_proxy_get_proxy')) {
        return twitter_proxy_get_proxy();
    }
    
    // 直接使用韩国AWS服务器代理
    return 'socks5://************:7890';
}

// 添加一个页面用于手动处理授权码
add_action('admin_menu', function() {
    add_submenu_page(
        'options-general.php',
        'Twitter令牌修复',
        'Twitter令牌修复',
        'manage_options',
        'twitter-token-fix',
        'twitter_token_fix_page'
    );
});

// 页面内容
function twitter_token_fix_page() {
    echo '<div class="wrap">';
    echo '<h1>Twitter OAuth令牌修复工具</h1>';
    
    // 处理表单提交
    if (isset($_POST['action']) && $_POST['action'] === 'exchange_token') {
        $code = isset($_POST['auth_code']) ? sanitize_text_field($_POST['auth_code']) : '';
        $verifier = isset($_POST['code_verifier']) ? sanitize_text_field($_POST['code_verifier']) : '';
        
        if (!empty($code) && !empty($verifier)) {
            $result = twitter_token_fix_exchange_token($code, $verifier);
            
            if ($result['success']) {
                echo '<div class="notice notice-success"><p>成功获取令牌！</p>';
                echo '<p>访问令牌: ' . substr($result['access_token'], 0, 10) . '...</p>';
                echo '<p>刷新令牌: ' . substr($result['refresh_token'], 0, 10) . '...</p>';
                echo '<p>已自动保存到WordPress设置中。</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>令牌交换失败: ' . $result['error'] . '</p></div>';
            }
        }
    }
    
    // 显示当前令牌状态
    $token = get_option('xyz_twap_tw_token');
    $refresh_token = get_option('xyz_twap_tw_refresh_token');
    $last_auth = get_option('xyz_twap_last_auth_time');
    
    echo '<h2>当前令牌状态</h2>';
    echo '<table class="form-table">';
    echo '<tr><th>访问令牌</th><td>' . (empty($token) ? '<span style="color:red">未设置</span>' : '<span style="color:green">已设置</span> (' . substr($token, 0, 10) . '...)') . '</td></tr>';
    echo '<tr><th>刷新令牌</th><td>' . (empty($refresh_token) ? '<span style="color:red">未设置</span>' : '<span style="color:green">已设置</span> (' . substr($refresh_token, 0, 10) . '...)') . '</td></tr>';
    echo '<tr><th>上次授权时间</th><td>' . (empty($last_auth) ? '未设置' : date('Y-m-d H:i:s', $last_auth)) . '</td></tr>';
    echo '</table>';
    
    // 表单
    echo '<h2>手动交换令牌</h2>';
    echo '<p>如果自动授权失败，您可以使用此表单手动交换令牌。</p>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="exchange_token">';
    echo '<table class="form-table">';
    echo '<tr><th>授权码 (Code)</th><td><input type="text" name="auth_code" class="regular-text" placeholder="从授权回调URL中的code参数获取"></td></tr>';
    echo '<tr><th>验证码 (Code Verifier)</th><td><input type="text" name="code_verifier" class="regular-text" placeholder="从Cookie中获取xyz_twap_code_verifier值"></td></tr>';
    echo '</table>';
    echo '<p class="submit"><input type="submit" class="button button-primary" value="交换令牌"></p>';
    echo '</form>';
    
    // 测试代理连接按钮
    echo '<h2>测试Twitter API连接</h2>';
    echo '<p>点击下面的按钮测试与Twitter API的连接：</p>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="test_connection">';
    echo '<p class="submit"><input type="submit" class="button" value="测试连接"></p>';
    echo '</form>';
    
    // 处理测试连接
    if (isset($_POST['action']) && $_POST['action'] === 'test_connection') {
        $test_result = twitter_token_fix_test_connection();
        if ($test_result['success']) {
            echo '<div class="notice notice-success"><p>成功连接到Twitter API！</p>';
            echo '<p>响应数据: ' . $test_result['response'] . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>连接失败: ' . $test_result['error'] . '</p></div>';
        }
    }
    
    echo '<h2>帮助信息</h2>';
    echo '<p>1. 访问Twitter设置页面并点击"Authorize"按钮</p>';
    echo '<p>2. 从回调URL中复制code参数的值</p>';
    echo '<p>3. 从您的Cookie中复制xyz_twap_code_verifier的值</p>';
    echo '<p>4. 填入上方表单并提交</p>';
    
    echo '</div>';
}

/**
 * 测试与Twitter API的连接
 */
function twitter_token_fix_test_connection() {
    $proxy = twitter_token_fix_get_proxy();
    $url = 'https://api.twitter.com/2/openapi.json';
    
    // 使用CURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, false);
    
    // 设置代理
    if (strpos($proxy, 'socks5') === 0) {
        curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // CURLPROXY_SOCKS5_HOSTNAME = 7
        $proxy_address = str_replace(['socks5://', 'socks5h://'], '', $proxy);
        $parts = explode(':', $proxy_address);
        if (count($parts) === 2) {
            curl_setopt($ch, CURLOPT_PROXY, $parts[0]);
            curl_setopt($ch, CURLOPT_PROXYPORT, $parts[1]);
        }
    } else {
        curl_setopt($ch, CURLOPT_PROXY, $proxy);
    }
    
    // 禁用SSL验证
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    // 设置TLS版本
    curl_setopt($ch, CURLOPT_SSLVERSION, 6); // CURL_SSLVERSION_TLSv1_2
    
    // 设置超时
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    
    // 强制IPv4
    curl_setopt($ch, CURLOPT_IPRESOLVE, 1); // CURL_IPRESOLVE_V4
    
    // 执行请求
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // 记录日志
    $log_file = WP_CONTENT_DIR . '/twitter-token-fix.log';
    $date = date('Y-m-d H:i:s');
    $log_data = "[$date] 测试连接到Twitter API\n";
    $log_data .= "代理: $proxy (韩国AWS服务器)\n";
    $log_data .= "HTTP状态码: $http_code\n";
    
    if ($error) {
        $log_data .= "CURL错误: $error\n";
        file_put_contents($log_file, $log_data, FILE_APPEND);
        
        return [
            'success' => false,
            'error' => "CURL错误: $error"
        ];
    }
    
    if ($http_code >= 200 && $http_code < 300) {
        $log_data .= "连接成功！\n";
        file_put_contents($log_file, $log_data, FILE_APPEND);
        return [
            'success' => true,
            'response' => '成功 (HTTP ' . $http_code . ')'
        ];
    } else {
        $log_data .= "连接失败: HTTP $http_code\n";
        file_put_contents($log_file, $log_data, FILE_APPEND);
        
        return [
            'success' => false,
            'error' => "HTTP错误: $http_code"
        ];
    }
}

// 手动交换令牌函数
function twitter_token_fix_exchange_token($code, $verifier) {
    // 获取必要设置
    $client_id = get_option('xyz_twap_client_id');
    $client_secret = get_option('xyz_twap_client_secret');
    $redirect_uri = admin_url('admin.php?page=twitter-auto-publish-settings');
    
    if (empty($client_id) || empty($client_secret)) {
        return [
            'success' => false,
            'error' => 'Client ID或Client Secret未设置'
        ];
    }
    
    // 获取代理
    $proxy = twitter_token_fix_get_proxy();
    $current_time = time();
    
    // 准备请求参数
    $token_url = 'https://api.twitter.com/2/oauth2/token';
    $data = [
        'code' => $code,
        'grant_type' => 'authorization_code',
        'code_verifier' => $verifier,
        'redirect_uri' => $redirect_uri,
    ];
    
    // 准备Basic Auth
    $auth_header = base64_encode("$client_id:$client_secret");
    
    // 使用CURL手动发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $token_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'Authorization: Basic ' . $auth_header
    ]);
    
    // 设置代理
    if (!empty($proxy)) {
        if (strpos($proxy, 'socks5') === 0) {
            curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // CURLPROXY_SOCKS5_HOSTNAME = 7
            $proxy_address = str_replace(['socks5://', 'socks5h://'], '', $proxy);
            $parts = explode(':', $proxy_address);
            if (count($parts) === 2) {
                curl_setopt($ch, CURLOPT_PROXY, $parts[0]);
                curl_setopt($ch, CURLOPT_PROXYPORT, $parts[1]);
            }
        } else {
            curl_setopt($ch, CURLOPT_PROXY, $proxy);
        }
    }
    
    // 关闭SSL验证
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    // 设置TLS版本
    curl_setopt($ch, CURLOPT_SSLVERSION, 6); // CURL_SSLVERSION_TLSv1_2
    
    // 设置超时
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    
    // 执行请求
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    // 记录请求信息
    $log_file = WP_CONTENT_DIR . '/twitter-token-fix.log';
    $date = date('Y-m-d H:i:s');
    $log_data = "[$date] 尝试交换令牌\n";
    $log_data .= "代理: $proxy\n";
    $log_data .= "HTTP状态: " . $info['http_code'] . "\n";
    if ($error) {
        $log_data .= "CURL错误: $error\n";
    }
    $log_data .= "响应内容: $response\n";
    $log_data .= "---\n";
    file_put_contents($log_file, $log_data, FILE_APPEND);
    
    // 处理响应
    if ($error) {
        // 标记当前代理失败
        if (function_exists('twitter_proxy_mark_failed')) {
            twitter_proxy_mark_failed();
        }
        
        return [
            'success' => false,
            'error' => "CURL错误: $error"
        ];
    }
    
    $data = json_decode($response, true);
    if (isset($data['access_token']) && isset($data['refresh_token'])) {
        // 保存令牌
        update_option('xyz_twap_tw_token', $data['access_token']);
        update_option('xyz_twap_tw_refresh_token', $data['refresh_token']);
        update_option('xyz_twap_last_auth_time', $current_time);
        update_option('xyz_twap_tw_af', 0);
        
        return [
            'success' => true,
            'access_token' => $data['access_token'],
            'refresh_token' => $data['refresh_token']
        ];
    } else {
        $error_msg = isset($data['error']) ? $data['error'] : '未知错误';
        $error_description = isset($data['error_description']) ? $data['error_description'] : '';
        
        // 标记当前代理失败
        if (function_exists('twitter_proxy_mark_failed')) {
            twitter_proxy_mark_failed();
        }
        
        return [
            'success' => false,
            'error' => "$error_msg: $error_description",
            'response' => $response
        ];
    }
}