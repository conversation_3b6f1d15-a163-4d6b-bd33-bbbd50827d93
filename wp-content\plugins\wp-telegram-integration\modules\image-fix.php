<?php
/**
 * 图片URL修复模块
 * 
 * 提供图片URL修复和额外的图片处理功能
 */

// 禁止直接访问
if (!defined('WPINC')) {
    die;
}

/**
 * 提取所有图片URL
 * 
 * @param string $content 文章内容
 * @return array 图片URL数组
 */
function wp_telegram_extract_all_images($content) {
    $images = array();
    preg_match_all('/<img.+src=[\'"]([^\'"]+)[\'"].*>/i', $content, $matches);
    
    if (isset($matches[1]) && !empty($matches[1])) {
        foreach ($matches[1] as $img_url) {
            $fixed_url = wp_telegram_fix_image_url($img_url);
            if ($fixed_url) {
                $images[] = $fixed_url;
            }
        }
    }
    
    return $images;
}

/**
 * 修复图片URL
 * 
 * @param string $image_url 原始图片URL
 * @return string 修复后的URL
 */
function wp_telegram_fix_image_url($image_url) {
    // 处理相对协议URL（以//开头的URL）
    if (strpos($image_url, '//') === 0) {
        $image_url = 'https:' . $image_url;
        wp_telegram_log('修复协议相对URL，添加https协议: ' . $image_url, 'debug');
    }
    // 处理其他相对URL
    else if (strpos($image_url, 'http') !== 0) {
        $site_domain = site_url();
        
        if (strpos($image_url, '/') === 0) {
            $image_url = $site_domain . $image_url;
        } else {
            $image_url = $site_domain . '/' . $image_url;
        }
        wp_telegram_log('修复相对URL，添加域名: ' . $image_url, 'debug');
    }
    
    return $image_url;
}

/**
 * 预加载图片以检查有效性
 * 
 * @param string $image_url 图片URL
 * @return bool 是否有效
 */
function wp_telegram_check_image_validity($image_url) {
    $args = array(
        'method' => 'HEAD',
        'timeout' => 10,
        'redirection' => 5,
        'httpversion' => '1.0',
        'blocking' => true,
        'headers' => array(),
        'cookies' => array(),
        'sslverify' => false
    );
    
    // 应用代理设置
    $args = wp_telegram_apply_proxy($args);
    
    wp_telegram_log('检查图片URL有效性: ' . $image_url, 'debug');
    $response = wp_remote_head($image_url, $args);
    
    if (is_wp_error($response)) {
        wp_telegram_log('图片URL无效: ' . $response->get_error_message(), 'warning');
        return false;
    }
    
    $status_code = wp_remote_retrieve_response_code($response);
    $content_type = wp_remote_retrieve_header($response, 'content-type');
    
    // 检查HTTP状态码和内容类型
    if ($status_code === 200 && strpos($content_type, 'image/') !== false) {
        wp_telegram_log('图片URL有效，Content-Type: ' . $content_type, 'debug');
        return true;
    } else {
        wp_telegram_log('图片URL无效，HTTP ' . $status_code . ', Content-Type: ' . $content_type, 'warning');
        return false;
    }
}

/**
 * 查找特色图片或文章第一张图片
 * 
 * @param int $post_id 文章ID
 * @param string $content 文章内容
 * @return string|bool 图片URL或false
 */
function wp_telegram_get_best_image($post_id, $content) {
    // 尝试获取特色图片
    if (has_post_thumbnail($post_id)) {
        $thumb_id = get_post_thumbnail_id($post_id);
        $thumb_url = wp_get_attachment_image_src($thumb_id, 'large');
        
        if ($thumb_url && !empty($thumb_url[0])) {
            wp_telegram_log('使用特色图片: ' . $thumb_url[0], 'debug');
            return $thumb_url[0];
        }
    }
    
    // 尝试从内容中提取第一张图片
    $image_url = wp_telegram_get_first_image_url($content);
    if ($image_url) {
        return $image_url;
    }
    
    return false;
}

/**
 * 发送多张图片（相册）
 * 
 * @param string $bot_token Bot Token
 * @param string $channel_id 频道ID
 * @param array $photo_urls 图片URL数组
 * @param string $caption 图片说明
 * @return bool|WP_Error 是否发送成功
 */
function wp_telegram_send_media_group($bot_token, $channel_id, $photo_urls, $caption = '') {
    if (empty($photo_urls) || count($photo_urls) === 0) {
        return new WP_Error('invalid_params', '没有提供图片URL');
    }
    
    // 如果只有一张图片，使用普通的图片发送方法
    if (count($photo_urls) === 1) {
        return wp_telegram_send_photo($bot_token, $channel_id, $photo_urls[0], $caption);
    }
    
    $url = "https://api.telegram.org/bot{$bot_token}/sendMediaGroup";
    
    // 准备媒体数组（最多10张图片）
    $media = array();
    $photo_urls = array_slice($photo_urls, 0, 10); // Telegram限制最多10张
    
    foreach ($photo_urls as $index => $photo_url) {
        $item = array(
            'type' => 'photo',
            'media' => $photo_url
        );
        
        // 只在第一个图片上添加说明
        if ($index === 0 && !empty($caption)) {
            $item['caption'] = $caption;
            $item['parse_mode'] = 'Markdown';
        }
        
        $media[] = $item;
    }
    
    $args = array(
        'body' => array(
            'chat_id' => $channel_id,
            'media' => json_encode($media)
        ),
        'timeout' => 60, // 增加超时时间，因为多张图片可能需要更长时间
        'redirection' => 5,
        'httpversion' => '1.0',
        'blocking' => true,
        'headers' => array(),
        'cookies' => array()
    );
    
    // 应用代理设置
    $args = wp_telegram_apply_proxy($args);
    
    wp_telegram_log('发送' . count($photo_urls) . '张图片到频道: ' . $channel_id, 'debug');
    $response = wp_remote_post($url, $args);
    
    if (is_wp_error($response)) {
        wp_telegram_log('相册发送失败: ' . $response->get_error_message(), 'error');
        return $response;
    }
    
    $body = json_decode(wp_remote_retrieve_body($response), true);
    $status_code = wp_remote_retrieve_response_code($response);
    
    if ($status_code !== 200 || !isset($body['ok']) || $body['ok'] !== true) {
        $error_message = isset($body['description']) ? $body['description'] : '未知错误 (HTTP ' . $status_code . ')';
        wp_telegram_log('API错误: ' . $error_message, 'error');
        
        // 如果相册发送失败，尝试回退到单张图片模式
        wp_telegram_log('相册发送失败，尝试使用单张图片模式', 'warning');
        return wp_telegram_send_photo($bot_token, $channel_id, $photo_urls[0], $caption);
    }
    
    wp_telegram_log('相册发送成功', 'debug');
    return true;
}

/**
 * 添加设置
 */
function wp_telegram_image_fix_settings($settings) {
    add_settings_field(
        'wp_telegram_integration_use_media_group',
        '使用相册模式',
        'wp_telegram_image_fix_settings_callback',
        'wp-telegram-integration',
        'wp_telegram_image_fix_section'
    );
    
    register_setting('wp_telegram_integration_options', 'wp_telegram_integration_use_media_group');
}
add_action('admin_init', 'wp_telegram_image_fix_settings');

/**
 * 设置回调函数
 */
function wp_telegram_image_fix_settings_callback() {
    $use_media_group = get_option('wp_telegram_integration_use_media_group', 'no');
    ?>
    <select name="wp_telegram_integration_use_media_group">
        <option value="no" <?php selected($use_media_group, 'no'); ?>>否 - 只发送第一张图片</option>
        <option value="yes" <?php selected($use_media_group, 'yes'); ?>>是 - 尝试发送多张图片</option>
    </select>
    <p class="description">
        启用此选项将尝试发送文章中的多张图片（最多10张）。请注意，当发送多张图片时，每张图片会单独占用消息上传限额。
    </p>
    <?php
}

/**
 * 添加设置部分
 */
function wp_telegram_image_fix_add_setting_section() {
    add_settings_section(
        'wp_telegram_image_fix_section',
        '图片处理选项',
        function() {
            echo '<p>配置图片处理和发送选项</p>';
        },
        'wp-telegram-integration'
    );
}
add_action('admin_init', 'wp_telegram_image_fix_add_setting_section'); 