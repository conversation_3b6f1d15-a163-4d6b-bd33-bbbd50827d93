{"name": "tinify/tiny-compress-images", "description": "Speed up your website. Optimize your JPEG, PNG, and WebP images automatically with TinyPNG.", "license": "GPL-2.0+", "type": "wordpress-plugin", "keywords": ["plugin"], "repositories": [{"type": "vcs", "url": "https://github.com/tinify/tinify-php"}], "require": {"php": ">=5.3.0", "composer/installers": "~1.0"}, "require-dev": {"phpunit/phpunit": "9.6.22", "mikey179/vfsstream": "~1.5", "mockery/mockery": "~0.9", "phpdocumentor/reflection-docblock": "~2.0", "squizlabs/php_codesniffer": "~2.2", "tinify/tinify": "dev-create-key", "wp-coding-standards/wpcs": "0.11"}, "scripts": {"post-install-cmd": "bin/post-install", "post-update-cmd": "bin/post-install"}, "config": {"allow-plugins": {"composer/installers": true}}}