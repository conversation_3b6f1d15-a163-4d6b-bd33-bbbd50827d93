<?php
/**
 * Plugin Name: Twitter API Proxy Helper
 * Description: 为Twitter API请求添加代理设置
 * Version: 2.0
 * Author: Admin
 */

// 禁止直接访问
if (!defined('ABSPATH')) {
    die;
}

/**
 * 日志记录函数
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别
 */
function twitter_proxy_log($message, $level = 'info') {
    $log_file = WP_CONTENT_DIR . '/twitter-proxy-debug.log';
    $date = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$date][$level] $message\n", FILE_APPEND);
}

/**
 * 获取代理服务器 - 优化版
 * 支持多服务器和故障转移
 *
 * @return string 可用的代理服务器地址
 */
function twitter_proxy_get_proxy() {
    // 代理服务器列表（按优先级排序）
    $proxy_servers = [
        'primary' => [
            'url' => 'socks5://************:7890',
            'name' => '韩国AWS首尔主服务器',
            'region' => 'ap-northeast-2'
        ]
        // 可以添加备用服务器
        // 'backup1' => [
        //     'url' => 'socks5://13.125.xxx.xxx:7890',
        //     'name' => '韩国AWS首尔备用服务器',
        //     'region' => 'ap-northeast-2'
        // ]
    ];

    // 检查缓存的可用代理
    $cached_proxy = get_transient('twitter_proxy_available');
    if ($cached_proxy && twitter_proxy_test_connection_quick($cached_proxy)) {
        twitter_proxy_log("使用缓存的代理: $cached_proxy", 'debug');
        return $cached_proxy;
    }

    // 测试每个代理服务器
    foreach ($proxy_servers as $key => $server) {
        if (twitter_proxy_test_connection_quick($server['url'])) {
            twitter_proxy_log("使用{$server['name']}: {$server['url']}", 'info');

            // 缓存可用的代理（5分钟）
            set_transient('twitter_proxy_available', $server['url'], 300);

            return $server['url'];
        } else {
            twitter_proxy_log("代理服务器不可用: {$server['name']}", 'warning');
        }
    }

    // 如果所有代理都不可用，返回主代理（让上层处理错误）
    $primary_proxy = $proxy_servers['primary']['url'];
    twitter_proxy_log("所有代理测试失败，返回主代理: $primary_proxy", 'error');
    return $primary_proxy;
}

/**
 * 快速测试代理连接
 *
 * @param string $proxy_url 代理URL
 * @return bool 是否可连接
 */
function twitter_proxy_test_connection_quick($proxy_url) {
    // 解析代理URL
    $parsed = parse_url($proxy_url);
    if (!$parsed || !isset($parsed['host'], $parsed['port'])) {
        return false;
    }

    // 使用fsockopen快速测试端口连通性
    $connection = @fsockopen($parsed['host'], $parsed['port'], $errno, $errstr, 5);
    if ($connection) {
        fclose($connection);
        return true;
    }

    return false;
}

/**
 * 给HTTP请求添加代理设置 - 简化版
 * 
 * @param array $args 请求参数
 * @param string $url 请求URL
 * @return array 应用代理后的请求参数
 */
function twitter_proxy_http_request($args, $url) {
    // 只对Twitter API请求应用代理
    if (strpos($url, 'api.twitter.com') !== false || strpos($url, 'upload.twitter.com') !== false || 
        strpos($url, 'api.x.com') !== false || strpos($url, 'x.com') !== false) {
        
        // 直接获取并应用韩国AWS代理
        $proxy = twitter_proxy_get_proxy();
            $args['proxy'] = $proxy;

        // 增加超时时间和禁用SSL验证
        $args['timeout'] = 120;
        $args['sslverify'] = false;
        $args['redirection'] = 5;
        $args['user-agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        
        twitter_proxy_log('应用代理到请求: ' . $url, 'info');
    }
    
    return $args;
}
add_filter('http_request_args', 'twitter_proxy_http_request', 10, 2);

/**
 * 给cURL请求添加SOCKS5代理设置 - 简化版
 * 
 * @param resource $handle cURL句柄
 * @param array $args 请求参数
 * @param string $url 请求URL
 * @return resource 设置代理后的cURL句柄
 */
function twitter_proxy_curl_handle($handle, $args, $url) {
    // 只对Twitter API请求应用代理
    if (strpos($url, 'api.twitter.com') !== false || strpos($url, 'upload.twitter.com') !== false || 
        strpos($url, 'api.x.com') !== false || strpos($url, 'x.com') !== false) {
        
        // 获取优化的代理配置
        $proxy_url = twitter_proxy_get_proxy();
        $parsed_proxy = parse_url($proxy_url);

        if ($parsed_proxy && isset($parsed_proxy['host'], $parsed_proxy['port'])) {
            // 应用韩国AWS代理
            curl_setopt($handle, CURLOPT_PROXY, $parsed_proxy['host']);
            curl_setopt($handle, CURLOPT_PROXYPORT, $parsed_proxy['port']);
            curl_setopt($handle, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5_HOSTNAME);

            // 针对AWS韩国服务器优化的超时设置
            curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 90);  // 连接超时90秒
            curl_setopt($handle, CURLOPT_TIMEOUT, 180);        // 总超时3分钟

            // SSL/TLS优化设置
            curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($handle, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);

            // 网络优化设置
            curl_setopt($handle, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
            curl_setopt($handle, CURLOPT_DNS_CACHE_TIMEOUT, 300); // DNS缓存5分钟

            // TCP连接优化（针对跨国网络）
            curl_setopt($handle, CURLOPT_TCP_KEEPALIVE, 1);
            curl_setopt($handle, CURLOPT_TCP_KEEPIDLE, 120);   // 2分钟后开始keepalive
            curl_setopt($handle, CURLOPT_TCP_KEEPINTVL, 60);   // keepalive间隔1分钟

            // HTTP优化设置
            curl_setopt($handle, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
            curl_setopt($handle, CURLOPT_ENCODING, ''); // 支持所有编码
            curl_setopt($handle, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($handle, CURLOPT_MAXREDIRS, 3);

            // 缓冲区优化（适合跨国传输）
            curl_setopt($handle, CURLOPT_BUFFERSIZE, 32768); // 32KB缓冲区

            twitter_proxy_log('已设置优化的cURL代理: ' . $parsed_proxy['host'] . ':' . $parsed_proxy['port'], 'info');
        } else {
            twitter_proxy_log('代理URL解析失败: ' . $proxy_url, 'error');
        }
        
        twitter_proxy_log('已设置cURL代理: ************:7890', 'info');
    }
    
    return $handle;
}
add_action('http_api_curl', 'twitter_proxy_curl_handle', 10, 3);

/**
 * 修改TwitterOAuth对象，添加代理设置 - 简化版
 */
function twitter_proxy_modify_twitteroauth_object() {
    // 检查TwitterOAuth类是否存在
    if (class_exists('Abraham\TwitterOAuth\TwitterOAuth')) {
        // 添加一个过滤器，在创建TwitterOAuth实例后修改它
        add_filter('xyz_twap_after_twitter_oauth_creation', function($instance) {
            if ($instance instanceof \Abraham\TwitterOAuth\TwitterOAuth) {
                // 统一设置代理参数
                    $instance->setProxy([
                        CURLOPT_PROXY => '************',
                        CURLOPT_PROXYPORT => 7890,
                        CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5_HOSTNAME,
                        CURLOPT_CONNECTTIMEOUT => 60,
                        CURLOPT_TIMEOUT => 120,
                        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
                        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
                        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    ]);

                    // 禁用SSL验证
                    $instance->setSSLVerification(false);

                    // 设置较长的超时时间
                    $instance->setTimeouts(120, 180);
                
                twitter_proxy_log('成功为TwitterOAuth实例设置代理', 'info');
            }
            
            return $instance;
        });
    }
}
add_action('init', 'twitter_proxy_modify_twitteroauth_object', 5);

/**
 * 设置全局代理变量 - 简化版
 */
function twitter_proxy_apply_global_proxy() {
    // 设置全局代理变量
    $GLOBALS['xyz_twap_proxy'] = [
        CURLOPT_PROXY => '************',
        CURLOPT_PROXYPORT => 7890,
        CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5_HOSTNAME,
        CURLOPT_CONNECTTIMEOUT => 60,
        CURLOPT_TIMEOUT => 120,
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ];
    
    // 禁用SSL验证
    add_filter('xyz_twap_peer_verification', function() { return 0; });
    
    twitter_proxy_log('已设置全局Twitter代理变量', 'info');
    
    // 添加发布钩子，设置环境变量
    add_action('xyz_twap_publish', function($postID) {
        twitter_proxy_log('准备发布文章到Twitter: ' . $postID, 'info');
        
        // 设置环境变量
        putenv('http_proxy=socks5://************:7890');
        putenv('https_proxy=socks5://************:7890');
        
        twitter_proxy_log('已设置环境变量代理', 'debug');
        
        // 注册关闭钩子，恢复环境变量
        register_shutdown_function(function() {
                putenv('http_proxy');
                putenv('https_proxy');
            twitter_proxy_log('已恢复环境变量代理', 'debug');
        });
    }, 1);
}
add_action('init', 'twitter_proxy_apply_global_proxy', 0);

/**
 * 处理Twitter API错误
 *
 * @param mixed $response API响应
 * @param string $context 上下文信息
 * @return bool 是否为可重试的错误
 */
function twitter_proxy_handle_api_error($response, $context = '') {
    if (is_wp_error($response)) {
        $error_code = $response->get_error_code();
        $error_message = $response->get_error_message();

        twitter_proxy_log("Twitter API错误 [$context]: $error_code - $error_message", 'error');

        // 检查是否为连接超时或网络错误（可重试）
        $retryable_errors = [
            'http_request_failed',
            'connect_timeout',
            'timeout',
            'ssl_connect_error',
            'connection_timeout'
        ];

        foreach ($retryable_errors as $retryable) {
            if (strpos($error_code, $retryable) !== false ||
                strpos($error_message, 'timeout') !== false ||
                strpos($error_message, 'Connection timed out') !== false ||
                strpos($error_message, 'SSL_connect') !== false) {
                return true; // 可重试
            }
        }

        return false; // 不可重试
    }

    return false;
}

/**
 * 添加重试机制和连接超时处理
 *
 * @param bool $result 原始发布结果
 * @param int $post_id 文章ID
 * @return bool 重试后的结果
 */
function twitter_proxy_add_retry_mechanism($result, $post_id) {
    // 如果发布失败，尝试重试
    if ($result === false || is_wp_error($result)) {
        $max_retries = 5; // 增加重试次数
        $retry_count = (int)get_post_meta($post_id, '_twitter_proxy_retry_count', true);

        if ($retry_count < $max_retries) {
            $retry_count++;
            update_post_meta($post_id, '_twitter_proxy_retry_count', $retry_count);
            twitter_proxy_log("发布失败，第 $retry_count 次重试 (文章ID: $post_id)", 'warning');

            // 根据重试次数增加等待时间
            $wait_time = min(10 + ($retry_count * 5), 30);
            twitter_proxy_log("等待 $wait_time 秒后重试", 'info');
            sleep($wait_time);

            // 获取原始发布函数
            if (function_exists('xyz_twap_publish_to_twitter')) {
                $post = get_post($post_id);
                $message = get_post_meta($post_id, 'xyz_twap_message', true);
                $image = get_post_meta($post_id, 'xyz_twap_img_url', true);

                // 重新尝试发布
                $result = xyz_twap_publish_to_twitter($message, $image, $post_id);
                twitter_proxy_log("重试结果: " . ($result === true ? '成功' : '失败'), 'info');
            }
        } else {
            twitter_proxy_log("已达到最大重试次数 ($max_retries)，放弃重试", 'error');
            delete_post_meta($post_id, '_twitter_proxy_retry_count');
        }
    } else {
        // 发布成功，清除重试计数
        delete_post_meta($post_id, '_twitter_proxy_retry_count');
    }
    
    return $result;
}
add_filter('xyz_twap_publish_result', 'twitter_proxy_add_retry_mechanism', 10, 2);

/**
 * 监控Twitter授权流程 - 保留原有功能
 */
function twitter_proxy_monitor_auth() {
    // 检查是否是Twitter回调
    if (isset($_REQUEST['code']) && isset($_REQUEST['state']) && 
        (strpos($_SERVER['REQUEST_URI'], 'twitter-auto-publish-settings') !== false || 
         strpos($_SERVER['REQUEST_URI'], 'oauth2/token') !== false)) {
        
        $log_file = WP_CONTENT_DIR . '/twitter-auth-callback.log';
        $date = date('Y-m-d H:i:s');
        $log_data = "[$date] 检测到Twitter回调\n";
        $log_data .= "URL: " . $_SERVER['REQUEST_URI'] . "\n";
        $log_data .= "Code: " . substr($_REQUEST['code'], 0, 10) . "...\n";
        $log_data .= "State: " . $_REQUEST['state'] . "\n";
        
        if (isset($_COOKIE['xyz_twap_session_state'])) {
            $log_data .= "Cookie state: " . $_COOKIE['xyz_twap_session_state'] . "\n";
            if ($_COOKIE['xyz_twap_session_state'] !== $_REQUEST['state']) {
                $log_data .= "警告: State不匹配!\n";
            }
        } else {
            $log_data .= "警告: Cookie xyz_twap_session_state不存在!\n";
        }
        
        if (isset($_COOKIE['xyz_twap_code_verifier'])) {
            $log_data .= "Code verifier存在 (长度: " . strlen($_COOKIE['xyz_twap_code_verifier']) . ")\n";
        } else {
            $log_data .= "警告: Cookie xyz_twap_code_verifier不存在!\n";
        }
        
        $log_data .= "完整请求参数: " . print_r($_REQUEST, true) . "\n";
        $log_data .= "完整Cookie: " . print_r($_COOKIE, true) . "\n";
        
        file_put_contents($log_file, $log_data . "\n---\n", FILE_APPEND);
        twitter_proxy_log('检测到Twitter授权回调，已记录详细日志', 'info');
    }
}
add_action('init', 'twitter_proxy_monitor_auth', 0);

/**
 * 添加一个测试页面，用于测试代理设置
 */
function twitter_proxy_add_test_page() {
    add_action('admin_menu', function() {
        add_submenu_page(
            'options-general.php',
            'Twitter代理测试',
            'Twitter代理测试',
            'manage_options',
            'twitter-proxy-test',
            'twitter_proxy_test_page_content'
        );
    });
    
    function twitter_proxy_test_page_content() {
        echo '<div class="wrap">';
        echo '<h1>Twitter代理测试</h1>';
        
        // 处理表单提交
        if (isset($_POST['twitter_test_action'])) {
            if ($_POST['twitter_test_action'] === 'test_proxy_curl') {
                // 测试PHP curl通过代理访问Twitter API
                echo '<h2>PHP curl通过韩国AWS代理访问Twitter API</h2>';
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://api.twitter.com/2/openapi.json');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_PROXY, '************');
                curl_setopt($ch, CURLOPT_PROXYPORT, 7890);
                curl_setopt($ch, CURLOPT_PROXYTYPE, 7); // SOCKS5
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_VERBOSE, true);
                
                $verbose = fopen('php://temp', 'w+');
                curl_setopt($ch, CURLOPT_STDERR, $verbose);
                
                $start = microtime(true);
                $response = curl_exec($ch);
                $time = round((microtime(true) - $start) * 1000);
                $error = curl_error($ch);
                $info = curl_getinfo($ch);
                
                rewind($verbose);
                $verboseLog = stream_get_contents($verbose);
                curl_close($ch);
                
                echo '<pre>';
                echo "结果: ";
                if ($error) {
                    echo "错误: $error\n";
                    echo "\n连接详细信息:\n$verboseLog\n";
                    echo "\n连接信息:\n";
                    print_r($info);
                } else {
                    echo "成功! HTTP状态码: {$info['http_code']} (响应时间: {$time}ms)\n";
                    echo "\n连接详细信息:\n$verboseLog\n";
                    echo "\n响应内容:\n" . substr($response, 0, 500) . "...\n";
                }
                echo '</pre>';
                
            } elseif ($_POST['twitter_test_action'] === 'test_oauth') {
                // 测试Twitter OAuth连接
                echo '<h2>测试Twitter OAuth连接</h2>';
                
                // 获取Twitter插件设置
                $client_id = get_option('xyz_twap_client_id');
                $client_secret = get_option('xyz_twap_client_secret');
                
                if (!class_exists('Abraham\TwitterOAuth\TwitterOAuth')) {
                    if (file_exists(WP_PLUGIN_DIR . '/twitter-auto-publish/vendor/autoload.php')) {
                        require_once(WP_PLUGIN_DIR . '/twitter-auto-publish/vendor/autoload.php');
                    }
                }
                
                if (class_exists('Abraham\TwitterOAuth\TwitterOAuth')) {
                    try {
                        // 记录日志
                        twitter_proxy_log('测试Twitter OAuth连接: 开始', 'info');
                        
                        // 创建TwitterOAuth实例
                        $connection = new \Abraham\TwitterOAuth\TwitterOAuth($client_id, $client_secret);
                        
                        // 设置代理
                        $connection->setProxy([
                            CURLOPT_PROXY => '************',
                            CURLOPT_PROXYPORT => 7890,
                            CURLOPT_PROXYTYPE => 7 // CURLPROXY_SOCKS5_HOSTNAME
                        ]);
                        
                        // 禁用SSL验证
                        $connection->setSSLVerification(false);
                        
                        // 设置超时
                        $connection->setTimeouts(60, 90);
                        
                        twitter_proxy_log('测试Twitter OAuth连接: 创建TwitterOAuth实例成功', 'info');
                        
                        // 生成OAuth URL
                        $redirect_uri = admin_url('admin.php?page=twitter-auto-publish-settings');
                        $oauth_callback = $redirect_uri;
                        
                        // 使用OAuth 2.0
                        $token = bin2hex(random_bytes(32));
                        $code_verifier = bin2hex(random_bytes(32));
                        $code_challenge = rtrim(strtr(base64_encode(hash('sha256', $code_verifier, true)), '+/', '-_'), '=');
                        
                        $oauth_url = $connection->getOAuth2AuthorizeURL($oauth_callback, ['code_challenge' => $code_challenge, 'code_challenge_method' => 'S256']);
                        
                        echo '<div class="notice notice-success"><p>OAuth实例创建成功!</p></div>';
                        echo '<p>授权URL:</p>';
                        echo '<pre>' . $oauth_url . '</pre>';
                        
                        echo '<p><a href="' . esc_url($oauth_url) . '" target="_blank" class="button button-primary">测试授权流程</a></p>';
                        
                    } catch (Exception $e) {
                        twitter_proxy_log('测试Twitter OAuth连接: 错误 - ' . $e->getMessage(), 'error');
                        echo '<div class="notice notice-error"><p>连接失败: ' . $e->getMessage() . '</p></div>';
                        echo '<pre>';
                        echo $e->getTraceAsString();
                        echo '</pre>';
                    }
                } else {
                    twitter_proxy_log('测试Twitter OAuth连接: TwitterOAuth类不存在', 'error');
                    echo '<div class="notice notice-error"><p>TwitterOAuth类不存在，无法测试。</p></div>';
                }
            } elseif ($_POST['twitter_test_action'] === 'debug_info') {
                // 显示调试信息
                echo '<h2>Twitter插件调试信息</h2>';
                
                // 获取Twitter插件设置
                $client_id = get_option('xyz_twap_client_id');
                $client_secret = get_option('xyz_twap_client_secret');
                $token = get_option('xyz_twap_tw_token');
                $refresh_token = get_option('xyz_twap_tw_refresh_token');
                $last_auth = get_option('xyz_twap_last_auth_time');
                
                echo '<pre>';
                echo "插件设置:\n";
                echo "Client ID: " . (empty($client_id) ? '未设置' : substr($client_id, 0, 5) . '...') . "\n";
                echo "Client Secret: " . (empty($client_secret) ? '未设置' : substr($client_secret, 0, 5) . '...') . "\n";
                echo "Access Token: " . (empty($token) ? '未设置' : substr($token, 0, 5) . '...') . "\n";
                echo "Refresh Token: " . (empty($refresh_token) ? '未设置' : substr($refresh_token, 0, 5) . '...') . "\n";
                echo "Last Auth Time: " . (empty($last_auth) ? '未设置' : date('Y-m-d H:i:s', $last_auth)) . "\n\n";
                
                echo "环境变量:\n";
                echo "http_proxy: " . getenv('http_proxy') . "\n";
                echo "https_proxy: " . getenv('https_proxy') . "\n";
                echo "\n\n";
                
                echo "PHP信息:\n";
                echo "PHP版本: " . phpversion() . "\n";
                echo "cURL支持: " . (function_exists('curl_version') ? '是' : '否') . "\n";
                if (function_exists('curl_version')) {
                    $curl_info = curl_version();
                    echo "cURL版本: " . $curl_info['version'] . "\n";
                    echo "SSL版本: " . $curl_info['ssl_version'] . "\n";
                    echo "支持的协议: " . implode(', ', $curl_info['protocols']) . "\n";
                }
                
                echo "\nWordPress代理设置:\n";
                echo "WP_PROXY_HOST: " . (defined('WP_PROXY_HOST') ? WP_PROXY_HOST : '未设置') . "\n";
                echo "WP_PROXY_PORT: " . (defined('WP_PROXY_PORT') ? WP_PROXY_PORT : '未设置') . "\n";
                echo "WP_PROXY_SOCKS5: " . (defined('WP_PROXY_SOCKS5') ? (WP_PROXY_SOCKS5 ? '是' : '否') : '未设置') . "\n";
                echo '</pre>';
                
                // 检查日志文件
                $log_files = [
                    'Twitter Proxy Debug' => WP_CONTENT_DIR . '/twitter-proxy-debug.log',
                    'Twitter Auth Callback' => WP_CONTENT_DIR . '/twitter-auth-callback.log',
                    'WordPress Debug' => WP_CONTENT_DIR . '/debug.log'
                ];
                
                echo '<h3>日志文件检查</h3>';
                foreach ($log_files as $name => $path) {
                    echo "<h4>$name</h4>";
                    if (file_exists($path)) {
                        $size = filesize($path);
                        $human_size = round($size / 1024, 2) . ' KB';
                        echo "<p>文件存在，大小: $human_size</p>";
                        
                        // 显示最后10行日志
                        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                        if ($lines) {
                            $last_lines = array_slice($lines, -10);
                            echo '<p>最后10行内容:</p>';
                            echo '<pre>' . implode("\n", $last_lines) . '</pre>';
                        }
                    } else {
                        echo "<p>文件不存在</p>";
                    }
                }
            }
        }
        
        // 测试表单
        echo '<form method="post">';
        echo '<input type="hidden" name="twitter_test_action" value="test_proxy_curl">';
        echo '<p><button type="submit" class="button button-primary">测试使用代理访问Twitter API (PHP curl)</button></p>';
        echo '</form>';
        
        echo '<form method="post">';
        echo '<input type="hidden" name="twitter_test_action" value="test_oauth">';
        echo '<p><button type="submit" class="button button-primary">测试Twitter OAuth连接</button></p>';
        echo '</form>';
        
        echo '<form method="post">';
        echo '<input type="hidden" name="twitter_test_action" value="debug_info">';
        echo '<p><button type="submit" class="button button-primary">显示调试信息</button></p>';
        echo '</form>';
        
        echo '<hr>';
        echo '<h2>说明</h2>';
        echo '<p>本插件只对Twitter API请求应用代理，不会影响网站其他功能。</p>';
        echo '<p>代理设置: <strong>socks5://************:7890</strong> (韩国AWS服务器)</p>';
        
        echo '</div>';
    }
}
add_action('admin_init', 'twitter_proxy_add_test_page');

/**
 * 添加Cookie处理改进 - 保留原有功能
 */
function twitter_proxy_enhance_cookies() {
    // 检查是否处于Twitter授权流程中
    if (isset($_GET['page']) && $_GET['page'] === 'twitter-auto-publish-settings') {
        // 确保可以设置和读取Cookie
        add_action('init', function() {
            if (!headers_sent()) {
                header('Set-Cookie: SameSite=None; Secure', false);
            }
        }, -1);
        
        // 如果是授权前页面，设置更长的Cookie过期时间
        if (isset($_POST['tw_auth']) || isset($_GET['auth'])) {
            add_action('init', function() {
                if (!headers_sent() && !isset($_COOKIE['xyz_twap_session_state'])) {
                    $twapp_session_state = md5(uniqid(rand(), TRUE));
                    $code_verifier = bin2hex(random_bytes(32));
                    
                    // 设置更长的Cookie过期时间
                    setcookie("xyz_twap_session_state", $twapp_session_state, time() + 3600, "/", "", is_ssl(), true);
                    setcookie("xyz_twap_code_verifier", $code_verifier, time() + 3600, "/", "", is_ssl(), true);
                    
                    twitter_proxy_log('已设置授权Cookie，有效期1小时', 'debug');
                }
            }, 0);
        }
    }
    
    // 处理OAuth回调
    if (isset($_GET['code']) && isset($_GET['state'])) {
        // 记录Cookie状态
        twitter_proxy_log('处理OAuth回调 - 状态: ' . $_GET['state'], 'debug');
        twitter_proxy_log('Cookie状态: ' . (isset($_COOKIE['xyz_twap_session_state']) ? $_COOKIE['xyz_twap_session_state'] : '未设置'), 'debug');
    }
}
add_action('init', 'twitter_proxy_enhance_cookies', 0);

// 插件激活时创建初始日志
register_activation_hook(__FILE__, function() {
    twitter_proxy_log('Twitter代理助手插件 v2.0 已激活', 'info');
    twitter_proxy_log('代理设置: 韩国AWS服务器 socks5://************:7890', 'info');
}); 